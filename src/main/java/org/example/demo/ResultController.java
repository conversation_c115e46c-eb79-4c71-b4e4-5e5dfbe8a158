package org.example.demo;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.control.TabPane;
import javafx.scene.control.TextField;
import javafx.scene.web.WebEngine;
import javafx.scene.web.WebView;
import javafx.stage.FileChooser;
import javafx.stage.Popup;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Cell;
import org.example.demo.api.FileResultInfoDTO;
import org.example.demo.api.ResultInfoDTO;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/30 10:26
 **/
public class ResultController {

    @FXML
    private TableColumn<ResultInfoDTO, String> labelColumn;

    @FXML
    private TableColumn<ResultInfoDTO, String> messageColumn;

    @FXML
    private TableView<ResultInfoDTO> tableView;

    @FXML
    private TableColumn<ResultInfoDTO, String> infoColumn;

    @FXML
    private TableColumn<ResultInfoDTO, String> infoValueColumn;


    @FXML
    private TableView<ResultInfoDTO> infoTableView;


    @FXML
    private ComboBox<String> comboBox;


//    @FXML
//    private TextFlow textFlow;

    @FXML
    private WebView webView;

    @FXML
    private WebView featureWebView;

    @FXML
    private TabPane chartTabPane;

    @FXML
    private TextField exportPathField;

    @FXML
    private BorderPane rootPane;


    /**
     * 解析结果
     */
    private static ObservableList<FileResultInfoDTO> resultList = FXCollections.observableArrayList();


    /**
     * 解析结果基础信息
     */
    public static Map<String, List<ResultInfoDTO>> baseInfoMap = new HashMap<>();

    public static ObservableList<String> resultFileList = FXCollections.observableArrayList();


    /**
     * -- SETTER --
     * in 文件内容
     *
     * @param inFileResultBuffer
     */
//    private static Map<String, String> inFileResult;


    /**
     * 时序图 - 支持多条曲线
     */
    public static Map<String, Map<String, Object>> timingDiagramMap = new HashMap<>();

    /**
     * HTML内容缓存，避免重复生成
     */
    private static Map<String, String> htmlContentCache = new HashMap<>();

    /**
     * 清理HTML缓存
     */
    public static void clearHtmlCache() {
        htmlContentCache.clear();
        System.out.println("已清理HTML缓存");
    }

    /**
     * 特性时序图
     */
    public static Map<String, Map<String, List>> ftimingDiagramFeatureMap = new HashMap<>();


    /**
     *Excel 导出数据集
     */
    public static Map<String, Map<String, List>> fileExcelDataMap = new HashMap<>();


//    public static void setTimingDiagramMap(Map<String, Map<String, List>> timingDiagramMap) {
//        ResultController.timingDiagramMap = timingDiagramMap;
//    }

    /**
     * 解析结果
     *
     * @param result
     */
    public static void setResult(List<FileResultInfoDTO> result) {
        List<String> resultFileList = resultList.stream().map(FileResultInfoDTO::getFileName).collect(Collectors.toList());
        for (FileResultInfoDTO fileResultInfoDTO : result) {
            if (!resultFileList.contains(fileResultInfoDTO.getFileName())) {
                resultList.add(fileResultInfoDTO);
            }
        }
    }


    /**
     * 文件夹下拉框
     *
     * @param fileList
     */
    public static void setFileList(List<String> fileList) {
        for (String s : fileList) {
            if (!resultFileList.contains(s)) {
                resultFileList.add(s);
            }
        }
    }


    public void initialize() {
        // 设置窗口自适应屏幕大小
        rootPane.sceneProperty().addListener((obs, oldScene, newScene) -> {
            if (newScene != null) {
                newScene.windowProperty().addListener((obs2, oldWindow, newWindow) -> {
                    if (newWindow != null) {
                        // 绑定窗口大小变化事件
                        newWindow.widthProperty().addListener((obs3, oldWidth, newWidth) -> {
                            // 调整组件大小
                            adjustComponentSizes();
                        });

                        newWindow.heightProperty().addListener((obs3, oldHeight, newHeight) -> {
                            // 调整组件大小
                            adjustComponentSizes();
                        });
                    }
                });
            }
        });

        labelColumn.setCellValueFactory(data -> data.getValue().labelProperty());
        infoColumn.setCellValueFactory(data -> data.getValue().labelProperty());
        infoValueColumn.setCellValueFactory(data -> data.getValue().messageProperty());

        // 设置自定义单元格工厂以支持自动换行
        // 设置单元格工厂以添加 Tooltip
        messageColumn.setCellFactory(col -> {
            return new TableCell<ResultInfoDTO, String>() {
                private Label label;
                private ScrollPane scrollPane;

                private Button closeButton;

                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                        setTooltip(null);
                    } else {
                        setText(truncateText(item, 10)); // 截断文本
                        setGraphic(null);

                        // 创建自定义 Tooltip
                        label = new Label(item);
                        label.setPadding(new Insets(5));
                        label.setStyle("-fx-background-color: rgba(255, 255, 255, 0.9); -fx-border-width: 1; -fx-border-color: black;");
                        label.setWrapText(true);
                        label.setMinWidth(300); // 固定宽度

                        // 创建关闭按钮
                        closeButton = new Button("关闭");
                        closeButton.setStyle("-fx-padding: 5px; -fx-background-color: #f0f0f0; -fx-border-color: #cccccc;");

                        // 创建包含关闭按钮的 HBox
                        HBox hBox = new HBox();
                        hBox.getChildren().addAll(label, closeButton);
                        hBox.setSpacing(10);
                        hBox.setPadding(new Insets(5));

                        // 创建 ScrollPane
                        scrollPane = new ScrollPane();
                        scrollPane.setContent(hBox);
                        scrollPane.setPrefSize(500, 500); // 固定高度为多行数据的高度
                        scrollPane.setFitToWidth(true);
                        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED); // 允许垂直滚动条

                        // 创建 Popup
                        Popup popup = new Popup();
                        popup.getContent().add(scrollPane);

                        // 设置关闭按钮的事件处理器
                        closeButton.setOnAction(event -> {
                            if (popup != null && popup.isShowing()) {
                                popup.hide();
                            }
                        });

                        // 添加鼠标点击事件处理器
                        setOnMouseClicked(event -> {
                            double offsetX = getScene().getWindow().getX() + getBoundsInParent().getMinX() + event.getX();
                            double offsetY = getScene().getWindow().getY() + getBoundsInParent().getMinY() + event.getY() + getHeight();
                            popup.show(getScene().getWindow(), offsetX, offsetY);
                        });
                    }
                }
            };
        });
        messageColumn.setCellValueFactory(data -> data.getValue().messageProperty());
        //默认展示第一个
        List<FileResultInfoDTO> fileResultInfoDTOS = resultList.stream().filter(t -> t.getFileName().equals(resultFileList.get(0))).collect(Collectors.toList());
        ObservableList<ResultInfoDTO> resultList = FXCollections.observableArrayList();
        if (fileResultInfoDTOS.isEmpty()) {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("提示");
            alert.setHeaderText(null);
            alert.setContentText("还未导入in文件，暂无结果展示。");
            alert.showAndWait();
            return;
        }

        resultList.addAll(fileResultInfoDTOS.get(0).getResultInfo());
        tableView.setItems(resultList);

        ObservableList<ResultInfoDTO> baseInfoList = FXCollections.observableArrayList();
        baseInfoList.addAll(baseInfoMap.get(resultFileList.get(0)));
        infoTableView.setItems(baseInfoList);

        // 设置自定义下拉框
//        ObservableList<String> options = FXCollections.observableArrayList(fileList);
        comboBox.setItems(resultFileList);

        // 设置默认选中项
        comboBox.setValue(resultFileList.get(0));


//        displayText(inFileResult.get(resultFileList.get(0)));

        show(resultFileList.get(0));


    }



    /**
     * 文件夹下拉框
     */
    @FXML
    public void comboBoxSelected() {
        //根据选择的下拉框文件夹区分展示信息
        String selectedOption = comboBox.getValue();
        List<FileResultInfoDTO> fileResultInfoDTOS = resultList.stream().filter(t -> t.getFileName().equals(selectedOption)).collect(Collectors.toList());
        ObservableList<ResultInfoDTO> resultList = FXCollections.observableArrayList();
        resultList.addAll(fileResultInfoDTOS.get(0).getResultInfo());
        tableView.setItems(resultList);

        ObservableList<ResultInfoDTO> baseInfoList = FXCollections.observableArrayList(baseInfoMap.get(selectedOption));
        infoTableView.setItems(baseInfoList);

        show(selectedOption);
        //in文件数据展示修改
//        displayText(inFileResult.get(selectedOption));
    }

    private void show(String selectedOption) {
        // 检查缓存
        String cachedHtml = htmlContentCache.get(selectedOption);
        if (cachedHtml != null) {
            System.out.println("使用缓存的HTML内容: " + selectedOption);
            WebEngine webEngine = webView.getEngine();
            webEngine.loadContent(cachedHtml);
            return;
        }

        //切换选择文件夹对应的时序图
        Map<String, Object> timingMap = timingDiagramMap.get(selectedOption);
        if (timingMap == null) {
            System.err.println("未找到数据: " + selectedOption);
            return;
        }

        // 调试输出，查看 x 轴数据
        List<String> xData = (List<String>) timingMap.get("x");
        List<Double> yLoadData = (List<Double>) timingMap.get("yL");
        Object yWeatherObj = timingMap.get("yW");

        String htmlContent;
        if (yWeatherObj instanceof Map) {
            // 多条气象曲线
            Map<String, List<Double>> yWeatherDataMap = (Map<String, List<Double>>) yWeatherObj;
            htmlContent = generateHtmlContentMultiCurve(xData, yLoadData, yWeatherDataMap);
        } else {
            // 单条气象曲线（兼容旧版本）
            List<Double> yWeatherData = (List<Double>) yWeatherObj;
            htmlContent = generateHtmlContent(xData, yLoadData, yWeatherData);
        }

        // 缓存HTML内容
        htmlContentCache.put(selectedOption, htmlContent);
        System.out.println("生成并缓存HTML内容: " + selectedOption);

//        Map<String, List> timingFeatureMap = ftimingDiagramFeatureMap.get(selectedOption);
//        String htmlFeatureContent = generateFeatureHtmlContent((List<String>) timingFeatureMap.get("x"), (List<Double>) timingFeatureMap.get("yL"), (List<Double>) timingFeatureMap.get("yW"));

        // 获取WebEngine并加载HTML内容
        WebEngine webEngine = webView.getEngine();
        webEngine.loadContent(htmlContent);

//        WebEngine featureEngine = featureWebView.getEngine();
//        featureEngine.loadContent(htmlFeatureContent);
    }

    private String generateHtmlContent(List<String> xData, List<Double> yData1, List<Double> yData2) {
        // 数据采样优化：如果数据量过大，进行智能采样
        DataSample sample = sampleData(xData, yData1, yData2);

        List<String> sampledXData = sample.xData;
        List<Double> resultLoad = sample.yData1;
        List<Double> resultWeather = sample.yData2;

        // 确保数据长度与 xData 一致，如果不足则在末尾补充 null
        while (resultLoad.size() < sampledXData.size()) {
            resultLoad.add(null);
        }

        while (resultWeather.size() < sampledXData.size()) {
            resultWeather.add(null);
        }
        // 将数据转换为 JSON 字符串
        String xDataStr = toJson(sampledXData);
        String yLoadDataStr = toJsonWithNull(resultLoad);
        String yWeatherDataStr = toJsonWithNull(resultWeather);

        // 计算 yData1 的范围和刻度间隔
        RangeAndTickInterval yData1Range = calculateRangeAndTickInterval(yData1);
        // 计算 yData2 的范围和刻度间隔
        RangeAndTickInterval yData2Range = calculateRangeAndTickInterval(yData2);
        // 获取 Plotly JavaScript 文件的路径
        String plotlyJsPath = Objects.requireNonNull(getClass().getResource("/js/plotly-latest.min.js")).toExternalForm();

        // 构建优化的 HTML 内容
        return generateOptimizedHtml(plotlyJsPath, xDataStr, yLoadDataStr, yWeatherDataStr, yData1Range, yData2Range, false);
    }

    /**
     * 生成支持多条气象曲线的HTML内容
     *
     * @param xData X轴数据（时间点）
     * @param yLoadData 负荷数据
     * @param yWeatherDataMap 气象数据映射（曲线名称 -> 数据列表）
     * @return HTML内容
     */
    private String generateHtmlContentMultiCurve(List<String> xData, List<Double> yLoadData, Map<String, List<Double>> yWeatherDataMap) {
        // 数据采样优化：如果数据量过大，进行智能采样
        MultiCurveDataSample sample = sampleMultiCurveData(xData, yLoadData, yWeatherDataMap);

        List<String> sampledXData = sample.xData;
        List<Double> resultLoad = sample.yLoadData;
        Map<String, List<Double>> processedWeatherDataMap = sample.yWeatherDataMap;

        // 确保负荷数据长度与 xData 一致
        while (resultLoad.size() < sampledXData.size()) {
            resultLoad.add(null);
        }

        // 处理气象数据，确保每条曲线的数据长度与 xData 一致
        for (Map.Entry<String, List<Double>> entry : processedWeatherDataMap.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            while (curveData.size() < sampledXData.size()) {
                curveData.add(null);
            }
        }

        // 将数据转换为 JSON 字符串
        String xDataStr = toJson(sampledXData);
        String yLoadDataStr = toJsonWithNull(resultLoad);

        // 计算负荷数据的范围和刻度间隔
        RangeAndTickInterval yLoadRange = calculateRangeAndTickInterval(yLoadData);

        // 计算气象数据的范围和刻度间隔（使用所有气象数据的最大值和最小值）
        List<Double> allWeatherData = new ArrayList<>();
        for (List<Double> curveData : yWeatherDataMap.values()) {
            allWeatherData.addAll(curveData.stream().filter(Objects::nonNull).toList());
        }
        RangeAndTickInterval yWeatherRange = calculateRangeAndTickInterval(allWeatherData);

        // 获取 Plotly JavaScript 文件的路径
        String plotlyJsPath = Objects.requireNonNull(getClass().getResource("/js/plotly-latest.min.js")).toExternalForm();

        // 构建优化的多曲线 HTML 内容
        return generateOptimizedMultiCurveHtml(plotlyJsPath, xDataStr, yLoadDataStr, processedWeatherDataMap, yLoadRange, yWeatherRange);
    }


    private String generateFeatureHtmlContent(List<String> xData, List<Double> yData1, List<Double> yData2) {
        List<Double> resultLoad = new ArrayList<>(yData1);
        List<Double> resultWeather = new ArrayList<>(yData2);

        // 确保数据长度与 xData 一致，如果不足则在末尾补充 null
        while (resultLoad.size() < xData.size()) {
            resultLoad.add(null);
        }

        while (resultWeather.size() < xData.size()) {
            resultWeather.add(null);
        }
        // 将数据转换为 JSON 字符串
        String xDataStr = toJson(xData);
        String yLoadDataStr = toJsonWithNull(resultLoad);
        String yWeatherDataStr = toJsonWithNull(resultWeather);

        // 计算 yData1 的范围和刻度间隔
        RangeAndTickInterval yData1Range = calculateRangeAndTickInterval(yData1);
        // 计算 yData2 的范围和刻度间隔
        RangeAndTickInterval yData2Range = calculateRangeAndTickInterval(yData2);
        // 获取 Plotly JavaScript 文件的路径
        String plotlyJsPath = Objects.requireNonNull(getClass().getResource("/js/plotly-latest.min.js")).toExternalForm();

        // 构建 HTML 内容

        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                String.format("    <script src=\"%s\"></script>\n", plotlyJsPath) +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n" +
                "        #plot { width: 100%; height: 600px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }\n" +
                "        h1 { color: #333; text-align: center; margin-bottom: 20px; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <h1>负荷气象特性时序图</h1>\n" +
                "    <div id=\"plot\"></div>\n" +
                "    <script>\n" +
                "// 检查 Plotly 是否已加载\n" +
                "        if (typeof Plotly === 'undefined') {\n" +
                "            console.error('Plotly is not defined. Ensure you have included the Plotly library correctly.');\n" +
                "        } else {\n" +
                "            var trace1 = {\n" +
                String.format("                x: %s,\n", xDataStr) +
                String.format("                y: %s,\n", yLoadDataStr) +
                "                mode: 'lines',\n" +
                "                line: { color: '#2C82C9', width: 2 },\n" +
                "                name: '负荷特性',\n" +
                "                yaxis: 'y1',\n" +
                "                visible: true\n" +
                "            };\n" +
                "\n" +
                "            var trace2 = {\n" +
                String.format("                x: %s,\n", xDataStr) +
                String.format("                y: %s,\n", yWeatherDataStr) +
                "                mode: 'lines',\n" +
                "                line: { color: '#EF4836', width: 2 },\n" +
                "                name: '温度特性',\n" +
                "                yaxis: 'y2',\n" +
                "                visible: true\n" +
                "            };\n" +
                "\n" +
                "            var data = [trace1, trace2];\n" +
                "\n" +
                "            // 添加选择条功能\n" +
                "            var updatemenus = [\n" +
                "                {\n" +
                "                    buttons: [\n" +
                "                        {\n" +
                "                            args: [{'visible': [true, true]}],\n" +
                "                            label: '显示全部',\n" +
                "                            method: 'update'\n" +
                "                        },\n" +
                "                        {\n" +
                "                            args: [{'visible': [true, false]}],\n" +
                "                            label: '仅显示负荷特性',\n" +
                "                            method: 'update'\n" +
                "                        },\n" +
                "                        {\n" +
                "                            args: [{'visible': [false, true]}],\n" +
                "                            label: '仅显示温度特性',\n" +
                "                            method: 'update'\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    direction: 'down',\n" +
                "                    pad: {'r': 10, 't': 10},\n" +
                "                    showactive: true,\n" +
                "                    type: 'dropdown',\n" +
                "                    x: 0.1,\n" +
                "                    xanchor: 'left',\n" +
                "                    y: 1.1,\n" +
                "                    yanchor: 'top'\n" +
                "                }\n" +
                "            ];\n" +
                "\n" +
                "            var layout = {\n" +
                "                title: { text: '负荷与气象特性数据对比', font: { size: 24 } },\n" +
                "                xaxis: {\n" +
                "                    title: { text: '日期', font: { size: 16, color: '#333' } },\n" +
                "                    type: 'category', // 使用类别类型，确保所有时间点都显示\n" +
                "                    tickmode: 'auto',\n" +
                "                    nticks: 24, // 控制显示的刻度数量\n" +
                "                    tickangle: 45, // 旋转刻度标签，使其更易读\n" +
                "                    tickfont: { size: 10 },\n" +
                "                    gridcolor: '#e0e0e0',\n" +
                "                    showgrid: true\n" +
                "                },\n" +
                "                yaxis: {\n" +
                "                    title: { text: '负荷特性值', font: { size: 16, color: '#2C82C9' } },\n" +
                "                    side: 'left',\n" +
                String.format("                    dtick: %.2f, // 设置纵坐标刻度间隔\n", yData1Range.tickInterval) +
                String.format("                    range: [%.2f, %.2f], // 设置纵坐标范围\n", yData1Range.min, yData1Range.max) +
                "                    autorange: false, // 禁用自动调整范围\n" +
                "                    tickfont: { color: '#2C82C9' },\n" +
                "                    gridcolor: '#e0e0e0',\n" +
                "                    showgrid: true\n" +
                "                },\n" +
                "                yaxis2: {\n" +
                "                    title: { text: '温度特性值', font: { size: 16, color: '#EF4836' } },\n" +
                "                    side: 'right',\n" +
                "                    overlaying: 'y',\n" +
                String.format("                    dtick: %.2f, // 设置纵坐标刻度间隔\n", yData2Range.tickInterval) +
                String.format("                    range: [%.2f, %.2f], // 设置纵坐标范围\n", yData2Range.min, yData2Range.max) +
                "                    autorange: false, // 禁用自动调整范围\n" +
                "                    tickfont: { color: '#EF4836' }\n" +
                "                },\n" +
                "                margin: { l: 60, r: 60, t: 80, b: 80 },\n" +
                "                paper_bgcolor: 'white',\n" +
                "                plot_bgcolor: 'white',\n" +
                "                hovermode: 'closest',\n" +
                "                legend: { orientation: 'h', y: -0.2 },\n" +
                "                updatemenus: updatemenus\n" +
                "            };\n" +
                "\n" +
                "            Plotly.newPlot('plot', data, layout);\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }

    // 将 String 数组转换为 JSON 字符串
    private String toJson(List<String> array) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < array.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            // 特殊处理 24:00 时间点，确保它能正确显示
            String value = array.get(i);
            sb.append("\"").append(value).append("\"");
        }
        sb.append("]");
        return sb.toString();
    }

    // 将 double 数组转换为 JSON 字符串，处理 NaN 值
    private String toJsonWithNull(List<Double> array) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < array.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            if (Objects.isNull(array.get(i)) || array.get(i).equals(Double.NaN)) {
                sb.append("null");
            } else {
                sb.append(array.get(i));
            }
        }
        sb.append("]");
        return sb.toString();
    }

    private RangeAndTickInterval calculateRangeAndTickInterval(List<Double> data) {
        OptionalDouble maxDouble = data.stream().filter(t->Objects.nonNull(t)&&!t.equals(Double.NaN)).mapToDouble(Double::doubleValue).max();
        OptionalDouble minDouble = data.stream().filter(t->Objects.nonNull(t)&&!t.equals(Double.NaN)).mapToDouble(Double::doubleValue).min();

        double max = maxDouble.orElse(0.0);
        double min = minDouble.orElse(0.0);
        double range = max - min;
        double rangeMin = min - range * 0.1; // 扩展 10%
        double rangeMax = max + range * 0.1; // 扩展 10%
        double tickInterval = (rangeMax - rangeMin) / 10; // 10 个刻度

        return new RangeAndTickInterval(rangeMin, rangeMax, tickInterval);
    }

    private static class RangeAndTickInterval {
        final double min;
        final double max;
        final double tickInterval;

        RangeAndTickInterval(double min, double max, double tickInterval) {
            this.min = min;
            this.max = max;
            this.tickInterval = tickInterval;
        }
    }

    /**
     * 数据采样结果类
     */
    private static class DataSample {
        final List<String> xData;
        final List<Double> yData1;
        final List<Double> yData2;

        DataSample(List<String> xData, List<Double> yData1, List<Double> yData2) {
            this.xData = xData;
            this.yData1 = yData1;
            this.yData2 = yData2;
        }
    }

    /**
     * 多曲线数据采样结果类
     */
    private static class MultiCurveDataSample {
        final List<String> xData;
        final List<Double> yLoadData;
        final Map<String, List<Double>> yWeatherDataMap;

        MultiCurveDataSample(List<String> xData, List<Double> yLoadData, Map<String, List<Double>> yWeatherDataMap) {
            this.xData = xData;
            this.yLoadData = yLoadData;
            this.yWeatherDataMap = yWeatherDataMap;
        }
    }

    /**
     * 智能数据采样：当数据量过大时进行采样以提高性能
     * 采用自适应采样算法，保留关键数据点
     */
    private DataSample sampleData(List<String> xData, List<Double> yData1, List<Double> yData2) {
        final int MAX_POINTS = 2000; // 最大显示点数

        if (xData.size() <= MAX_POINTS) {
            // 数据量不大，不需要采样
            return new DataSample(new ArrayList<>(xData), new ArrayList<>(yData1), new ArrayList<>(yData2));
        }

        // 计算采样间隔
        int step = xData.size() / MAX_POINTS;
        if (step < 2) step = 2;

        List<String> sampledX = new ArrayList<>();
        List<Double> sampledY1 = new ArrayList<>();
        List<Double> sampledY2 = new ArrayList<>();

        // 保留第一个和最后一个数据点
        sampledX.add(xData.get(0));
        sampledY1.add(yData1.get(0));
        sampledY2.add(yData2.get(0));

        // 采样中间数据点
        for (int i = step; i < xData.size() - 1; i += step) {
            sampledX.add(xData.get(i));
            sampledY1.add(yData1.get(i));
            sampledY2.add(yData2.get(i));
        }

        // 保留最后一个数据点
        if (xData.size() > 1) {
            sampledX.add(xData.get(xData.size() - 1));
            sampledY1.add(yData1.get(yData1.size() - 1));
            sampledY2.add(yData2.get(yData2.size() - 1));
        }

        System.out.println("数据采样：原始数据点数 " + xData.size() + " -> 采样后数据点数 " + sampledX.size());

        return new DataSample(sampledX, sampledY1, sampledY2);
    }

    /**
     * 多曲线数据采样
     */
    private MultiCurveDataSample sampleMultiCurveData(List<String> xData, List<Double> yLoadData, Map<String, List<Double>> yWeatherDataMap) {
        final int MAX_POINTS = 2000; // 最大显示点数

        if (xData.size() <= MAX_POINTS) {
            // 数据量不大，不需要采样
            Map<String, List<Double>> copiedWeatherData = new HashMap<>();
            for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                copiedWeatherData.put(entry.getKey(), new ArrayList<>(entry.getValue()));
            }
            return new MultiCurveDataSample(new ArrayList<>(xData), new ArrayList<>(yLoadData), copiedWeatherData);
        }

        // 计算采样间隔
        int step = xData.size() / MAX_POINTS;
        if (step < 2) step = 2;

        List<String> sampledX = new ArrayList<>();
        List<Double> sampledLoad = new ArrayList<>();
        Map<String, List<Double>> sampledWeatherData = new HashMap<>();

        // 初始化气象数据集合
        for (String curveName : yWeatherDataMap.keySet()) {
            sampledWeatherData.put(curveName, new ArrayList<>());
        }

        // 保留第一个数据点
        sampledX.add(xData.get(0));
        sampledLoad.add(yLoadData.get(0));
        for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
            sampledWeatherData.get(entry.getKey()).add(entry.getValue().get(0));
        }

        // 采样中间数据点
        for (int i = step; i < xData.size() - 1; i += step) {
            sampledX.add(xData.get(i));
            sampledLoad.add(yLoadData.get(i));
            for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                sampledWeatherData.get(entry.getKey()).add(entry.getValue().get(i));
            }
        }

        // 保留最后一个数据点
        if (xData.size() > 1) {
            sampledX.add(xData.get(xData.size() - 1));
            sampledLoad.add(yLoadData.get(yLoadData.size() - 1));
            for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                List<Double> curveData = entry.getValue();
                sampledWeatherData.get(entry.getKey()).add(curveData.get(curveData.size() - 1));
            }
        }

        System.out.println("多曲线数据采样：原始数据点数 " + xData.size() + " -> 采样后数据点数 " + sampledX.size());

        return new MultiCurveDataSample(sampledX, sampledLoad, sampledWeatherData);
    }

    /**
     * 生成优化的单曲线HTML内容
     */
    private String generateOptimizedHtml(String plotlyJsPath, String xDataStr, String yLoadDataStr, String yWeatherDataStr,
                                        RangeAndTickInterval yData1Range, RangeAndTickInterval yData2Range, boolean isMultiCurve) {
        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                String.format("    <script src=\"%s\"></script>\n", plotlyJsPath) +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n" +
                "        #plot { width: 100%; height: 600px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }\n" +
                "        h1 { color: #333; text-align: center; margin-bottom: 20px; }\n" +
                "        .loading { text-align: center; padding: 50px; color: #666; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <h1>负荷气象时序图</h1>\n" +
                "    <div id=\"plot\">\n" +
                "        <div class=\"loading\">加载中...</div>\n" +
                "    </div>\n" +
                "    <script>\n" +
                "        // 优化的Plotly配置\n" +
                "        if (typeof Plotly === 'undefined') {\n" +
                "            console.error('Plotly is not defined.');\n" +
                "            document.getElementById('plot').innerHTML = '<div class=\"loading\">加载失败</div>';\n" +
                "        } else {\n" +
                "            // 使用requestAnimationFrame优化渲染\n" +
                "            requestAnimationFrame(function() {\n" +
                "                var trace1 = {\n" +
                String.format("                    x: %s,\n", xDataStr) +
                String.format("                    y: %s,\n", yLoadDataStr) +
                "                    mode: 'lines',\n" +
                "                    line: { color: '#2C82C9', width: 1.5 },\n" +
                "                    name: '负荷',\n" +
                "                    yaxis: 'y1',\n" +
                "                    hovertemplate: '<b>负荷</b><br>时间: %{x}<br>数值: %{y}<extra></extra>'\n" +
                "                };\n" +
                "\n" +
                "                var trace2 = {\n" +
                String.format("                    x: %s,\n", xDataStr) +
                String.format("                    y: %s,\n", yWeatherDataStr) +
                "                    mode: 'lines',\n" +
                "                    line: { color: '#EF4836', width: 1.5 },\n" +
                "                    name: '温度',\n" +
                "                    yaxis: 'y2',\n" +
                "                    hovertemplate: '<b>温度</b><br>时间: %{x}<br>数值: %{y}<extra></extra>'\n" +
                "                };\n" +
                "\n" +
                "                var data = [trace1, trace2];\n" +
                "\n" +
                "                var layout = {\n" +
                "                    title: { text: '负荷与气象数据对比', font: { size: 20 } },\n" +
                "                    xaxis: {\n" +
                "                        title: { text: '时间', font: { size: 14 } },\n" +
                "                        type: 'category',\n" +
                "                        tickangle: -45,\n" +
                "                        automargin: true,\n" +
                "                        rangeslider: { visible: true, thickness: 0.1 }\n" +
                "                    },\n" +
                "                    yaxis: {\n" +
                "                        title: { text: '负荷值', font: { size: 14, color: '#2C82C9' } },\n" +
                "                        side: 'left',\n" +
                String.format("                        range: [%.2f, %.2f],\n", yData1Range.min, yData1Range.max) +
                String.format("                        dtick: %.2f,\n", yData1Range.tickInterval) +
                "                        tickfont: { color: '#2C82C9' }\n" +
                "                    },\n" +
                "                    yaxis2: {\n" +
                "                        title: { text: '温度值', font: { size: 14, color: '#EF4836' } },\n" +
                "                        side: 'right',\n" +
                "                        overlaying: 'y',\n" +
                String.format("                        range: [%.2f, %.2f],\n", yData2Range.min, yData2Range.max) +
                String.format("                        dtick: %.2f,\n", yData2Range.tickInterval) +
                "                        tickfont: { color: '#EF4836' }\n" +
                "                    },\n" +
                "                    margin: { l: 60, r: 80, t: 60, b: 80 },\n" +
                "                    hovermode: 'x unified',\n" +
                "                    legend: { x: 1.02, y: 1, xanchor: 'left', yanchor: 'top' },\n" +
                "                    dragmode: 'zoom'\n" +
                "                };\n" +
                "\n" +
                "                // 优化的配置选项\n" +
                "                var config = {\n" +
                "                    responsive: true,\n" +
                "                    displayModeBar: true,\n" +
                "                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],\n" +
                "                    doubleClick: 'reset+autosize'\n" +
                "                };\n" +
                "\n" +
                "                Plotly.newPlot('plot', data, layout, config);\n" +
                "            });\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * 生成优化的多曲线HTML内容
     */
    private String generateOptimizedMultiCurveHtml(String plotlyJsPath, String xDataStr, String yLoadDataStr,
                                                  Map<String, List<Double>> processedWeatherDataMap,
                                                  RangeAndTickInterval yLoadRange, RangeAndTickInterval yWeatherRange) {
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<!DOCTYPE html>\n")
                .append("<html>\n")
                .append("<head>\n")
                .append(String.format("    <script src=\"%s\"></script>\n", plotlyJsPath))
                .append("    <style>\n")
                .append("        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n")
                .append("        #plot { width: 100%; height: 600px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }\n")
                .append("        h1 { color: #333; text-align: center; margin-bottom: 20px; }\n")
                .append("        .loading { text-align: center; padding: 50px; color: #666; }\n")
                .append("    </style>\n")
                .append("</head>\n")
                .append("<body>\n")
                .append("    <h1>负荷气象时序图（多曲线）</h1>\n")
                .append("    <div id=\"plot\">\n")
                .append("        <div class=\"loading\">加载中...</div>\n")
                .append("    </div>\n")
                .append("    <script>\n")
                .append("        if (typeof Plotly === 'undefined') {\n")
                .append("            console.error('Plotly is not defined.');\n")
                .append("            document.getElementById('plot').innerHTML = '<div class=\"loading\">加载失败</div>';\n")
                .append("        } else {\n")
                .append("            requestAnimationFrame(function() {\n");

        // 添加负荷曲线
        htmlBuilder.append("                var trace_load = {\n")
                .append(String.format("                    x: %s,\n", xDataStr))
                .append(String.format("                    y: %s,\n", yLoadDataStr))
                .append("                    mode: 'lines',\n")
                .append("                    line: { color: '#2C82C9', width: 1.5 },\n")
                .append("                    name: '负荷',\n")
                .append("                    yaxis: 'y1',\n")
                .append("                    hovertemplate: '<b>负荷</b><br>时间: %{x}<br>数值: %{y}<extra></extra>'\n")
                .append("                };\n\n");

        // 添加气象曲线
        String[] colors = {"#EF4836", "#28A745", "#FFC107", "#6F42C1", "#FD7E14", "#20C997", "#E83E8C", "#6C757D"};
        int colorIndex = 0;

        for (Map.Entry<String, List<Double>> entry : processedWeatherDataMap.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            String curveDataStr = toJsonWithNull(curveData);
            String color = colors[colorIndex % colors.length];

            htmlBuilder.append(String.format("                var trace_%s = {\n", curveName.replaceAll("[^a-zA-Z0-9]", "_")))
                    .append(String.format("                    x: %s,\n", xDataStr))
                    .append(String.format("                    y: %s,\n", curveDataStr))
                    .append("                    mode: 'lines',\n")
                    .append(String.format("                    line: { color: '%s', width: 1.5 },\n", color))
                    .append(String.format("                    name: '%s',\n", curveName))
                    .append("                    yaxis: 'y2',\n")
                    .append(String.format("                    hovertemplate: '<b>%s</b><br>时间: %%{x}<br>数值: %%{y}<extra></extra>'\n", curveName))
                    .append("                };\n\n");

            colorIndex++;
        }

        // 添加数据数组
        htmlBuilder.append("                var data = [trace_load");
        for (String curveName : processedWeatherDataMap.keySet()) {
            htmlBuilder.append(", trace_").append(curveName.replaceAll("[^a-zA-Z0-9]", "_"));
        }
        htmlBuilder.append("];\n\n");

        // 添加布局配置
        htmlBuilder.append("                var layout = {\n")
                .append("                    title: { text: '负荷气象时序图（多曲线）', font: { size: 20 } },\n")
                .append("                    xaxis: {\n")
                .append("                        title: { text: '时间', font: { size: 14 } },\n")
                .append("                        type: 'category',\n")
                .append("                        tickangle: -45,\n")
                .append("                        automargin: true,\n")
                .append("                        rangeslider: { visible: true, thickness: 0.1 }\n")
                .append("                    },\n")
                .append("                    yaxis: {\n")
                .append("                        title: { text: '负荷值', font: { size: 14, color: '#2C82C9' } },\n")
                .append("                        side: 'left',\n")
                .append(String.format("                        range: [%.2f, %.2f],\n", yLoadRange.min, yLoadRange.max))
                .append(String.format("                        dtick: %.2f\n", yLoadRange.tickInterval))
                .append("                    },\n")
                .append("                    yaxis2: {\n")
                .append("                        title: { text: '气象值', font: { size: 14 } },\n")
                .append("                        side: 'right',\n")
                .append("                        overlaying: 'y',\n")
                .append(String.format("                        range: [%.2f, %.2f],\n", yWeatherRange.min, yWeatherRange.max))
                .append(String.format("                        dtick: %.2f\n", yWeatherRange.tickInterval))
                .append("                    },\n")
                .append("                    legend: {\n")
                .append("                        x: 1.02,\n")
                .append("                        y: 1,\n")
                .append("                        xanchor: 'left',\n")
                .append("                        yanchor: 'top'\n")
                .append("                    },\n")
                .append("                    hovermode: 'x unified',\n")
                .append("                    margin: { l: 60, r: 120, t: 60, b: 80 },\n")
                .append("                    dragmode: 'zoom'\n")
                .append("                };\n\n")
                .append("                var config = {\n")
                .append("                    responsive: true,\n")
                .append("                    displayModeBar: true,\n")
                .append("                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],\n")
                .append("                    doubleClick: 'reset+autosize'\n")
                .append("                };\n\n")
                .append("                Plotly.newPlot('plot', data, layout, config);\n")
                .append("            });\n")
                .append("        }\n")
                .append("    </script>\n")
                .append("</body>\n")
                .append("</html>");

        return htmlBuilder.toString();
    }

    // 截断文本的方法
    private String truncateText(String text, int length) {
        if (text.length() > length) {
            return text.substring(0, length) + "...";
        }
        return text;
    }


    public void handleExportSinge(ActionEvent event) {
        Map<String, List> stringListMap = fileExcelDataMap.get(comboBox.getValue());
        FileChooser fileChooser = new FileChooser();
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Excel Files", "*.xlsx")
        );
        File file = fileChooser.showSaveDialog(null);

        if (file != null) {
            try (Workbook workbook = new HSSFWorkbook()) {
                mergeExcelData(stringListMap, workbook);
                try (FileOutputStream outputStream = new FileOutputStream(file)) {
                    workbook.write(outputStream);
                }

                System.out.println("Excel file has been created successfully.");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void mergeExcelData(Map<String, List> stringListMap, Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        setBorderStyle(cellStyle);
        for (Map.Entry<String, List> stringListEntry : stringListMap.entrySet()) {
            String sheetValue = stringListEntry.getKey();
            List value = stringListEntry.getValue();
            Sheet sheet = workbook.createSheet(sheetValue);

            for (int i = 0; i < value.size(); i++) {
                Row row = sheet.createRow(i);
                String[] rowData = (String[]) value.get(i);
                for (int j = 0; j < rowData.length; j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellValue(rowData[j]);
                    cell.setCellStyle(cellStyle);
                }
            }
        }
    }

    private static void setBorderStyle(CellStyle cellStyle) {
        BorderStyle border = BorderStyle.THIN;
        cellStyle.setBorderTop(border);
        cellStyle.setBorderBottom(border);
        cellStyle.setBorderLeft(border);
        cellStyle.setBorderRight(border);
    }

    public void handleExport(ActionEvent event) {
        try {

            if (fileExcelDataMap.size() ==1){
                handleExportSinge(event);
                return;
            }
            // 1. 创建临时文件夹
            File tempDir = createTempDirectory();

            // 2. 生成多个Excel文件
            generateExcelFiles(tempDir);

            // 3. 打包成ZIP
            File zipFile = packToZip(tempDir);

            // 4. 保存对话框
            saveZipFile(zipFile);

            // 5. 清理临时文件
            deleteTempFiles(tempDir);

        } catch (IOException ex) {
            ex.printStackTrace();
            // 这里可以添加错误提示弹窗
        }
    }

    private File createTempDirectory() throws IOException {
        File tempDir = File.createTempFile("excelExport", Long.toString(System.nanoTime()));
        tempDir.delete();
        tempDir.mkdir();
        return tempDir;
    }

    private void generateExcelFiles(File dir) throws IOException {

        for (Map.Entry<String, Map<String, List>> entry : fileExcelDataMap.entrySet()) {
            String directoryName = entry.getKey();
            Map<String, List> stringListMap = entry.getValue();
            try (Workbook workbook = new HSSFWorkbook()) {
                // 创建一个带有边框的单元格样式
                mergeExcelData(stringListMap, workbook);
                try (FileOutputStream outputStream = new FileOutputStream(new File(dir,
                        directoryName.split("/")[directoryName.split("/").length-1].replace(".e","") + ".xlsx"))) {
                    workbook.write(outputStream);
                }

                System.out.println("Excel file has been created successfully.");
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }
    private File packToZip(File sourceDir) throws IOException {
        File zipFile = File.createTempFile("export", ".zip");
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {

            for (File file : sourceDir.listFiles()) {
                ZipEntry zipEntry = new ZipEntry(file.getName());
                zos.putNextEntry(zipEntry);

                try (FileInputStream fis = new FileInputStream(file)) {
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) > 0) {
                        zos.write(buffer, 0, length);
                    }
                }
                zos.closeEntry();
            }
        }
        return zipFile;
    }

    private void saveZipFile(File zipFile) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("保存压缩包");
        fileChooser.setInitialFileName("export_data.zip");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("ZIP Files", "*.zip")
        );

        File saveFile = fileChooser.showSaveDialog(null);
        if (saveFile != null) {
            try (InputStream in = new FileInputStream(zipFile);
                 OutputStream out = new FileOutputStream(saveFile)) {

                byte[] buffer = new byte[1024];
                int length;
                while ((length = in.read(buffer)) > 0) {
                    out.write(buffer, 0, length);
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

    private void deleteTempFiles(File tempDir) {
        for (File file : tempDir.listFiles()) {
            file.delete();
        }
        tempDir.delete();
    }

    @FXML
    private void exportToExcel(ActionEvent event) {
        String exportPath = exportPathField.getText().trim();
        if (exportPath.isEmpty()) {
            showAlert(Alert.AlertType.ERROR, "错误", "请输入导出文件路径");
            return;
        }

        // 确保路径以 .xlsx 结尾
        if (!exportPath.toLowerCase().endsWith(".xlsx")) {
            exportPath += ".xlsx";
        }

        try {
            // 获取当前选中的文件夹
            String selectedFolder = comboBox.getSelectionModel().getSelectedItem();
            if (selectedFolder == null) {
                showAlert(Alert.AlertType.ERROR, "错误", "请先选择一个文件夹");
                return;
            }

            // 获取异常信息数据
            List<ResultInfoDTO> errorData = tableView.getItems();
            if (errorData.isEmpty()) {
                showAlert(Alert.AlertType.WARNING, "警告", "没有异常数据可导出");
                return;
            }

            // 创建工作簿和工作表
            Workbook workbook = new HSSFWorkbook();
            Sheet sheet = workbook.createSheet("异常数据");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("标签");
            headerRow.createCell(1).setCellValue("信息");

            // 填充数据
            for (int i = 0; i < errorData.size(); i++) {
                ResultInfoDTO data = errorData.get(i);
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(data.getLabelName());
                row.createCell(1).setCellValue(data.getMessage());
            }

            // 自动调整列宽
            for (int i = 0; i < 2; i++) {
                sheet.autoSizeColumn(i);
            }

            // 保存工作簿到文件
            try (FileOutputStream fileOut = new FileOutputStream(exportPath)) {
                workbook.write(fileOut);
            }

            showAlert(Alert.AlertType.INFORMATION, "成功", "异常数据已成功导出到: " + exportPath);

        } catch (Exception e) {
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "错误", "导出失败: " + e.getMessage());
        }
    }

    private void showAlert(Alert.AlertType alertType, String title, String message) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 调整组件大小以适应窗口大小
     */
    private void adjustComponentSizes() {
        if (rootPane.getScene() == null || rootPane.getScene().getWindow() == null) {
            return;
        }

        // 获取当前窗口大小
        double windowWidth = rootPane.getScene().getWindow().getWidth();
        double windowHeight = rootPane.getScene().getWindow().getHeight();

        // 调整 rootPane 的大小
        rootPane.setPrefWidth(windowWidth);
        rootPane.setPrefHeight(windowHeight);

        // 调整 WebView 的大小
        if (webView != null) {
            webView.setPrefWidth(windowWidth * 0.7); // 使用窗口宽度的70%
            webView.setPrefHeight(windowHeight * 0.8); // 使用窗口高度的80%
        }

        if (featureWebView != null) {
            featureWebView.setPrefWidth(windowWidth * 0.7);
            featureWebView.setPrefHeight(windowHeight * 0.8);
        }

        // 调整表格列宽
        if (labelColumn != null && messageColumn != null) {
            double tableWidth = windowWidth * 0.3 - 20; // 减去一些边距
            labelColumn.setPrefWidth(tableWidth * 0.3); // 30%
            messageColumn.setPrefWidth(tableWidth * 0.7); // 70%
        }

        if (infoColumn != null && infoValueColumn != null) {
            double tableWidth = windowWidth * 0.3 - 20;
            infoColumn.setPrefWidth(tableWidth * 0.3);
            infoValueColumn.setPrefWidth(tableWidth * 0.7);
        }
    }
}
