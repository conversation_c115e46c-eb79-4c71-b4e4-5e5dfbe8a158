package org.example.demo;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.event.ActionEvent;
import javafx.fxml.FXML;
import javafx.geometry.Insets;
import javafx.scene.control.*;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.HBox;
import javafx.scene.control.TabPane;
import javafx.scene.control.TextField;
import javafx.scene.web.WebEngine;
import javafx.scene.web.WebView;
import javafx.stage.FileChooser;
import javafx.stage.Popup;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.Cell;
import org.example.demo.api.FileResultInfoDTO;
import org.example.demo.api.ResultInfoDTO;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/8/30 10:26
 **/
public class ResultController {

    @FXML
    private TableColumn<ResultInfoDTO, String> labelColumn;

    @FXML
    private TableColumn<ResultInfoDTO, String> messageColumn;

    @FXML
    private TableView<ResultInfoDTO> tableView;

    @FXML
    private TableColumn<ResultInfoDTO, String> infoColumn;

    @FXML
    private TableColumn<ResultInfoDTO, String> infoValueColumn;


    @FXML
    private TableView<ResultInfoDTO> infoTableView;


    @FXML
    private ComboBox<String> comboBox;


//    @FXML
//    private TextFlow textFlow;

    @FXML
    private WebView webView;

    @FXML
    private WebView featureWebView;

    @FXML
    private TabPane chartTabPane;

    @FXML
    private TextField exportPathField;

    @FXML
    private BorderPane rootPane;


    /**
     * 解析结果
     */
    private static ObservableList<FileResultInfoDTO> resultList = FXCollections.observableArrayList();


    /**
     * 解析结果基础信息
     */
    public static Map<String, List<ResultInfoDTO>> baseInfoMap = new HashMap<>();

    public static ObservableList<String> resultFileList = FXCollections.observableArrayList();


    /**
     * -- SETTER --
     * in 文件内容
     *
     * @param inFileResultBuffer
     */
//    private static Map<String, String> inFileResult;


    /**
     * 时序图 - 支持多条曲线
     */
    public static Map<String, Map<String, Object>> timingDiagramMap = new HashMap<>();

    /**
     * HTML内容缓存，避免重复生成
     */
    private static Map<String, String> htmlContentCache = new HashMap<>();

    /**
     * 清理HTML缓存
     */
    public static void clearHtmlCache() {
        htmlContentCache.clear();
        System.out.println("已清理HTML缓存");
    }

    /**
     * 特性时序图
     */
    public static Map<String, Map<String, List>> ftimingDiagramFeatureMap = new HashMap<>();


    /**
     *Excel 导出数据集
     */
    public static Map<String, Map<String, List>> fileExcelDataMap = new HashMap<>();


//    public static void setTimingDiagramMap(Map<String, Map<String, List>> timingDiagramMap) {
//        ResultController.timingDiagramMap = timingDiagramMap;
//    }

    /**
     * 解析结果
     *
     * @param result
     */
    public static void setResult(List<FileResultInfoDTO> result) {
        List<String> resultFileList = resultList.stream().map(FileResultInfoDTO::getFileName).collect(Collectors.toList());
        for (FileResultInfoDTO fileResultInfoDTO : result) {
            if (!resultFileList.contains(fileResultInfoDTO.getFileName())) {
                resultList.add(fileResultInfoDTO);
            }
        }
    }


    /**
     * 文件夹下拉框
     *
     * @param fileList
     */
    public static void setFileList(List<String> fileList) {
        for (String s : fileList) {
            if (!resultFileList.contains(s)) {
                resultFileList.add(s);
            }
        }
    }


    public void initialize() {
        // 设置窗口自适应屏幕大小
        rootPane.sceneProperty().addListener((obs, oldScene, newScene) -> {
            if (newScene != null) {
                newScene.windowProperty().addListener((obs2, oldWindow, newWindow) -> {
                    if (newWindow != null) {
                        // 绑定窗口大小变化事件
                        newWindow.widthProperty().addListener((obs3, oldWidth, newWidth) -> {
                            // 调整组件大小
                            adjustComponentSizes();
                        });

                        newWindow.heightProperty().addListener((obs3, oldHeight, newHeight) -> {
                            // 调整组件大小
                            adjustComponentSizes();
                        });
                    }
                });
            }
        });

        labelColumn.setCellValueFactory(data -> data.getValue().labelProperty());
        infoColumn.setCellValueFactory(data -> data.getValue().labelProperty());
        infoValueColumn.setCellValueFactory(data -> data.getValue().messageProperty());

        // 设置自定义单元格工厂以支持自动换行
        // 设置单元格工厂以添加 Tooltip
        messageColumn.setCellFactory(col -> {
            return new TableCell<ResultInfoDTO, String>() {
                private Label label;
                private ScrollPane scrollPane;

                private Button closeButton;

                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                        setTooltip(null);
                    } else {
                        setText(truncateText(item, 10)); // 截断文本
                        setGraphic(null);

                        // 创建自定义 Tooltip
                        label = new Label(item);
                        label.setPadding(new Insets(5));
                        label.setStyle("-fx-background-color: rgba(255, 255, 255, 0.9); -fx-border-width: 1; -fx-border-color: black;");
                        label.setWrapText(true);
                        label.setMinWidth(300); // 固定宽度

                        // 创建关闭按钮
                        closeButton = new Button("关闭");
                        closeButton.setStyle("-fx-padding: 5px; -fx-background-color: #f0f0f0; -fx-border-color: #cccccc;");

                        // 创建包含关闭按钮的 HBox
                        HBox hBox = new HBox();
                        hBox.getChildren().addAll(label, closeButton);
                        hBox.setSpacing(10);
                        hBox.setPadding(new Insets(5));

                        // 创建 ScrollPane
                        scrollPane = new ScrollPane();
                        scrollPane.setContent(hBox);
                        scrollPane.setPrefSize(500, 500); // 固定高度为多行数据的高度
                        scrollPane.setFitToWidth(true);
                        scrollPane.setVbarPolicy(ScrollPane.ScrollBarPolicy.AS_NEEDED); // 允许垂直滚动条

                        // 创建 Popup
                        Popup popup = new Popup();
                        popup.getContent().add(scrollPane);

                        // 设置关闭按钮的事件处理器
                        closeButton.setOnAction(event -> {
                            if (popup != null && popup.isShowing()) {
                                popup.hide();
                            }
                        });

                        // 添加鼠标点击事件处理器
                        setOnMouseClicked(event -> {
                            double offsetX = getScene().getWindow().getX() + getBoundsInParent().getMinX() + event.getX();
                            double offsetY = getScene().getWindow().getY() + getBoundsInParent().getMinY() + event.getY() + getHeight();
                            popup.show(getScene().getWindow(), offsetX, offsetY);
                        });
                    }
                }
            };
        });
        messageColumn.setCellValueFactory(data -> data.getValue().messageProperty());
        //默认展示第一个
        List<FileResultInfoDTO> fileResultInfoDTOS = resultList.stream().filter(t -> t.getFileName().equals(resultFileList.get(0))).collect(Collectors.toList());
        ObservableList<ResultInfoDTO> resultList = FXCollections.observableArrayList();
        if (fileResultInfoDTOS.isEmpty()) {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("提示");
            alert.setHeaderText(null);
            alert.setContentText("还未导入in文件，暂无结果展示。");
            alert.showAndWait();
            return;
        }

        resultList.addAll(fileResultInfoDTOS.get(0).getResultInfo());
        tableView.setItems(resultList);

        ObservableList<ResultInfoDTO> baseInfoList = FXCollections.observableArrayList();
        baseInfoList.addAll(baseInfoMap.get(resultFileList.get(0)));
        infoTableView.setItems(baseInfoList);

        // 设置自定义下拉框
//        ObservableList<String> options = FXCollections.observableArrayList(fileList);
        comboBox.setItems(resultFileList);

        // 设置默认选中项
        comboBox.setValue(resultFileList.get(0));


//        displayText(inFileResult.get(resultFileList.get(0)));

        show(resultFileList.get(0));


    }



    /**
     * 文件夹下拉框
     */
    @FXML
    public void comboBoxSelected() {
        //根据选择的下拉框文件夹区分展示信息
        String selectedOption = comboBox.getValue();
        List<FileResultInfoDTO> fileResultInfoDTOS = resultList.stream().filter(t -> t.getFileName().equals(selectedOption)).collect(Collectors.toList());
        ObservableList<ResultInfoDTO> resultList = FXCollections.observableArrayList();
        resultList.addAll(fileResultInfoDTOS.get(0).getResultInfo());
        tableView.setItems(resultList);

        ObservableList<ResultInfoDTO> baseInfoList = FXCollections.observableArrayList(baseInfoMap.get(selectedOption));
        infoTableView.setItems(baseInfoList);

        show(selectedOption);
        //in文件数据展示修改
//        displayText(inFileResult.get(selectedOption));
    }

    private void show(String selectedOption) {
        // 检查缓存
        String cachedHtml = htmlContentCache.get(selectedOption);
        if (cachedHtml != null) {
            System.out.println("使用缓存的HTML内容: " + selectedOption);
            WebEngine webEngine = webView.getEngine();
            webEngine.loadContent(cachedHtml);
            return;
        }

        //切换选择文件夹对应的时序图
        Map<String, Object> timingMap = timingDiagramMap.get(selectedOption);
        if (timingMap == null) {
            System.err.println("未找到数据: " + selectedOption);
            return;
        }

        // 调试输出，查看 x 轴数据
        List<String> xData = (List<String>) timingMap.get("x");
        List<Double> yLoadData = (List<Double>) timingMap.get("yL");
        Object yWeatherObj = timingMap.get("yW");

        String htmlContent;
        if (yWeatherObj instanceof Map) {
            // 多条气象曲线 - 使用动态数据加载
            Map<String, List<Double>> yWeatherDataMap = (Map<String, List<Double>>) yWeatherObj;
            htmlContent = generateDynamicHtmlContentMultiCurve(selectedOption, xData, yLoadData, yWeatherDataMap);
        } else {
            // 单条气象曲线 - 使用动态数据加载
            List<Double> yWeatherData = (List<Double>) yWeatherObj;
            htmlContent = generateDynamicHtmlContent(selectedOption, xData, yLoadData, yWeatherData);
        }

        // 缓存HTML内容
        htmlContentCache.put(selectedOption, htmlContent);
        System.out.println("生成并缓存动态HTML内容: " + selectedOption);

        // 获取WebEngine并加载HTML内容
        WebEngine webEngine = webView.getEngine();
        webEngine.loadContent(htmlContent);
    }

    private String generateHtmlContent(List<String> xData, List<Double> yData1, List<Double> yData2) {
        // 数据采样优化：如果数据量过大，进行智能采样
        DataSample sample = sampleData(xData, yData1, yData2);

        List<String> sampledXData = sample.xData;
        List<Double> resultLoad = sample.yData1;
        List<Double> resultWeather = sample.yData2;

        // 确保数据长度与 xData 一致，如果不足则在末尾补充 null
        while (resultLoad.size() < sampledXData.size()) {
            resultLoad.add(null);
        }

        while (resultWeather.size() < sampledXData.size()) {
            resultWeather.add(null);
        }
        // 将数据转换为 JSON 字符串
        String xDataStr = toJson(sampledXData);
        String yLoadDataStr = toJsonWithNull(resultLoad);
        String yWeatherDataStr = toJsonWithNull(resultWeather);

        // 计算 yData1 的范围和刻度间隔
        RangeAndTickInterval yData1Range = calculateRangeAndTickInterval(yData1);
        // 计算 yData2 的范围和刻度间隔
        RangeAndTickInterval yData2Range = calculateRangeAndTickInterval(yData2);
        // 获取 Plotly JavaScript 文件的路径
        String plotlyJsPath = Objects.requireNonNull(getClass().getResource("/js/plotly-latest.min.js")).toExternalForm();

        // 构建优化的 HTML 内容
        return generateOptimizedHtml(sampledXData,plotlyJsPath, xDataStr, yLoadDataStr, yWeatherDataStr, yData1Range, yData2Range, false);
    }

    /**
     * 生成支持多条气象曲线的HTML内容
     *
     * @param xData X轴数据（时间点）
     * @param yLoadData 负荷数据
     * @param yWeatherDataMap 气象数据映射（曲线名称 -> 数据列表）
     * @return HTML内容
     */
    private String generateHtmlContentMultiCurve(List<String> xData, List<Double> yLoadData, Map<String, List<Double>> yWeatherDataMap) {
        // 数据采样优化：如果数据量过大，进行智能采样
        MultiCurveDataSample sample = sampleMultiCurveData(xData, yLoadData, yWeatherDataMap);

        List<String> sampledXData = sample.xData;
        List<Double> resultLoad = sample.yLoadData;
        Map<String, List<Double>> processedWeatherDataMap = sample.yWeatherDataMap;

        // 确保负荷数据长度与 xData 一致
        while (resultLoad.size() < sampledXData.size()) {
            resultLoad.add(null);
        }

        // 处理气象数据，确保每条曲线的数据长度与 xData 一致
        for (Map.Entry<String, List<Double>> entry : processedWeatherDataMap.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            while (curveData.size() < sampledXData.size()) {
                curveData.add(null);
            }
        }

        // 将数据转换为 JSON 字符串
        String xDataStr = toJson(sampledXData);
        String yLoadDataStr = toJsonWithNull(resultLoad);

        // 计算负荷数据的范围和刻度间隔
        RangeAndTickInterval yLoadRange = calculateRangeAndTickInterval(yLoadData);

        // 计算气象数据的范围和刻度间隔（使用所有气象数据的最大值和最小值）
        List<Double> allWeatherData = new ArrayList<>();
        for (List<Double> curveData : yWeatherDataMap.values()) {
            allWeatherData.addAll(curveData.stream().filter(Objects::nonNull).toList());
        }
        RangeAndTickInterval yWeatherRange = calculateRangeAndTickInterval(allWeatherData);

        // 获取 Plotly JavaScript 文件的路径
        String plotlyJsPath = Objects.requireNonNull(getClass().getResource("/js/plotly-latest.min.js")).toExternalForm();

        // 构建优化的多曲线 HTML 内容
        return generateOptimizedMultiCurveHtml(sampledXData,plotlyJsPath, xDataStr, yLoadDataStr, processedWeatherDataMap, yLoadRange, yWeatherRange);
    }


    private String generateFeatureHtmlContent(List<String> xData, List<Double> yData1, List<Double> yData2) {
        List<Double> resultLoad = new ArrayList<>(yData1);
        List<Double> resultWeather = new ArrayList<>(yData2);

        // 确保数据长度与 xData 一致，如果不足则在末尾补充 null
        while (resultLoad.size() < xData.size()) {
            resultLoad.add(null);
        }

        while (resultWeather.size() < xData.size()) {
            resultWeather.add(null);
        }
        // 将数据转换为 JSON 字符串
        String xDataStr = toJson(xData);
        String yLoadDataStr = toJsonWithNull(resultLoad);
        String yWeatherDataStr = toJsonWithNull(resultWeather);

        // 计算 yData1 的范围和刻度间隔
        RangeAndTickInterval yData1Range = calculateRangeAndTickInterval(yData1);
        // 计算 yData2 的范围和刻度间隔
        RangeAndTickInterval yData2Range = calculateRangeAndTickInterval(yData2);
        // 获取 Plotly JavaScript 文件的路径
        String plotlyJsPath = Objects.requireNonNull(getClass().getResource("/js/plotly-latest.min.js")).toExternalForm();

        // 构建 HTML 内容

        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                String.format("    <script src=\"%s\"></script>\n", plotlyJsPath) +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n" +
                "        #plot { width: 100%; height: 600px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }\n" +
                "        h1 { color: #333; text-align: center; margin-bottom: 20px; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <h1>负荷气象特性时序图</h1>\n" +
                "    <div id=\"plot\"></div>\n" +
                "    <script>\n" +
                "// 检查 Plotly 是否已加载\n" +
                "        if (typeof Plotly === 'undefined') {\n" +
                "            console.error('Plotly is not defined. Ensure you have included the Plotly library correctly.');\n" +
                "        } else {\n" +
                "            var trace1 = {\n" +
                String.format("                x: %s,\n", xDataStr) +
                String.format("                y: %s,\n", yLoadDataStr) +
                "                mode: 'lines',\n" +
                "                line: { color: '#2C82C9', width: 2 },\n" +
                "                name: '负荷特性',\n" +
                "                yaxis: 'y1',\n" +
                "                visible: true\n" +
                "            };\n" +
                "\n" +
                "            var trace2 = {\n" +
                String.format("                x: %s,\n", xDataStr) +
                String.format("                y: %s,\n", yWeatherDataStr) +
                "                mode: 'lines',\n" +
                "                line: { color: '#EF4836', width: 2 },\n" +
                "                name: '温度特性',\n" +
                "                yaxis: 'y2',\n" +
                "                visible: true\n" +
                "            };\n" +
                "\n" +
                "            var data = [trace1, trace2];\n" +
                "\n" +
                "            // 添加选择条功能\n" +
                "            var updatemenus = [\n" +
                "                {\n" +
                "                    buttons: [\n" +
                "                        {\n" +
                "                            args: [{'visible': [true, true]}],\n" +
                "                            label: '显示全部',\n" +
                "                            method: 'update'\n" +
                "                        },\n" +
                "                        {\n" +
                "                            args: [{'visible': [true, false]}],\n" +
                "                            label: '仅显示负荷特性',\n" +
                "                            method: 'update'\n" +
                "                        },\n" +
                "                        {\n" +
                "                            args: [{'visible': [false, true]}],\n" +
                "                            label: '仅显示温度特性',\n" +
                "                            method: 'update'\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    direction: 'down',\n" +
                "                    pad: {'r': 10, 't': 10},\n" +
                "                    showactive: true,\n" +
                "                    type: 'dropdown',\n" +
                "                    x: 0.1,\n" +
                "                    xanchor: 'left',\n" +
                "                    y: 1.1,\n" +
                "                    yanchor: 'top'\n" +
                "                }\n" +
                "            ];\n" +
                "\n" +
                "            var layout = {\n" +
                "                title: { text: '负荷与气象特性数据对比', font: { size: 24 } },\n" +
                "                xaxis: {\n" +
                "                    title: { text: '日期', font: { size: 16, color: '#333' } },\n" +
                "                    type: 'category', // 使用类别类型，确保所有时间点都显示\n" +
                "                    tickmode: 'auto',\n" +
                "                    nticks: 24, // 控制显示的刻度数量\n" +
                "                    tickangle: 45, // 旋转刻度标签，使其更易读\n" +
                "                    tickfont: { size: 10 },\n" +
                "                    gridcolor: '#e0e0e0',\n" +
                "                    showgrid: true\n" +
                "                },\n" +
                "                yaxis: {\n" +
                "                    title: { text: '负荷特性值', font: { size: 16, color: '#2C82C9' } },\n" +
                "                    side: 'left',\n" +
                String.format("                    dtick: %.2f, // 设置纵坐标刻度间隔\n", yData1Range.tickInterval) +
                String.format("                    range: [%.2f, %.2f], // 设置纵坐标范围\n", yData1Range.min, yData1Range.max) +
                "                    autorange: false, // 禁用自动调整范围\n" +
                "                    tickfont: { color: '#2C82C9' },\n" +
                "                    gridcolor: '#e0e0e0',\n" +
                "                    showgrid: true\n" +
                "                },\n" +
                "                yaxis2: {\n" +
                "                    title: { text: '温度特性值', font: { size: 16, color: '#EF4836' } },\n" +
                "                    side: 'right',\n" +
                "                    overlaying: 'y',\n" +
                String.format("                    dtick: %.2f, // 设置纵坐标刻度间隔\n", yData2Range.tickInterval) +
                String.format("                    range: [%.2f, %.2f], // 设置纵坐标范围\n", yData2Range.min, yData2Range.max) +
                "                    autorange: false, // 禁用自动调整范围\n" +
                "                    tickfont: { color: '#EF4836' }\n" +
                "                },\n" +
                "                margin: { l: 60, r: 60, t: 80, b: 80 },\n" +
                "                paper_bgcolor: 'white',\n" +
                "                plot_bgcolor: 'white',\n" +
                "                hovermode: 'closest',\n" +
                "                legend: { orientation: 'h', y: -0.2 },\n" +
                "                updatemenus: updatemenus\n" +
                "            };\n" +
                "\n" +
                "            Plotly.newPlot('plot', data, layout);\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }

    // 将 String 数组转换为 JSON 字符串
    private String toJson(List<String> array) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < array.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            // 特殊处理 24:00 时间点，确保它能正确显示
            String value = array.get(i);
            sb.append("\"").append(value).append("\"");
        }
        sb.append("]");
        return sb.toString();
    }

    // 将 double 数组转换为 JSON 字符串，处理 NaN 值
    private String toJsonWithNull(List<Double> array) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < array.size(); i++) {
            if (i > 0) {
                sb.append(",");
            }
            if (Objects.isNull(array.get(i)) || array.get(i).equals(Double.NaN)) {
                sb.append("null");
            } else {
                sb.append(array.get(i));
            }
        }
        sb.append("]");
        return sb.toString();
    }

    private RangeAndTickInterval calculateRangeAndTickInterval(List<Double> data) {
        OptionalDouble maxDouble = data.stream().filter(t->Objects.nonNull(t)&&!t.equals(Double.NaN)).mapToDouble(Double::doubleValue).max();
        OptionalDouble minDouble = data.stream().filter(t->Objects.nonNull(t)&&!t.equals(Double.NaN)).mapToDouble(Double::doubleValue).min();

        double max = maxDouble.orElse(0.0);
        double min = minDouble.orElse(0.0);
        double range = max - min;
        double rangeMin = min - range * 0.1; // 扩展 10%
        double rangeMax = max + range * 0.1; // 扩展 10%
        double tickInterval = (rangeMax - rangeMin) / 10; // 10 个刻度

        return new RangeAndTickInterval(rangeMin, rangeMax, tickInterval);
    }

    private static class RangeAndTickInterval {
        final double min;
        final double max;
        final double tickInterval;

        RangeAndTickInterval(double min, double max, double tickInterval) {
            this.min = min;
            this.max = max;
            this.tickInterval = tickInterval;
        }
    }

    /**
         * 数据采样结果类
         */
        private record DataSample(List<String> xData, List<Double> yData1, List<Double> yData2) {
    }

    /**
     * 多曲线数据采样结果类
     */
    private static class MultiCurveDataSample {
        final List<String> xData;
        final List<Double> yLoadData;
        final Map<String, List<Double>> yWeatherDataMap;

        MultiCurveDataSample(List<String> xData, List<Double> yLoadData, Map<String, List<Double>> yWeatherDataMap) {
            this.xData = xData;
            this.yLoadData = yLoadData;
            this.yWeatherDataMap = yWeatherDataMap;
        }
    }

    /**
     * 智能数据采样：当数据量过大时进行采样以提高性能
     * 采用自适应采样算法，保留关键数据点，但保持所有时间点信息
     */
    private DataSample sampleData(List<String> xData, List<Double> yData1, List<Double> yData2) {
        final int MAX_POINTS = 3000; // 提高最大显示点数，保证更好的显示效果

        if (xData.size() <= MAX_POINTS) {
            // 数据量不大，不需要采样，但需要保证数据一致性
            return new DataSample(new ArrayList<>(xData), new ArrayList<>(yData1), new ArrayList<>(yData2));
        }

        // 计算采样间隔，使用更精细的采样策略
        int step = Math.max(2, xData.size() / MAX_POINTS);

        List<String> sampledX = new ArrayList<>();
        List<Double> sampledY1 = new ArrayList<>();
        List<Double> sampledY2 = new ArrayList<>();

        // 保留第一个数据点
        sampledX.add(xData.get(0));
        sampledY1.add(yData1.size() > 0 ? yData1.get(0) : null);
        sampledY2.add(yData2.size() > 0 ? yData2.get(0) : null);

        // 采样中间数据点，确保均匀分布
        for (int i = step; i < xData.size() - 1; i += step) {
            sampledX.add(xData.get(i));
            sampledY1.add(i < yData1.size() ? yData1.get(i) : null);
            sampledY2.add(i < yData2.size() ? yData2.get(i) : null);
        }

        // 保留最后一个数据点
        if (xData.size() > 1) {
            int lastIndex = xData.size() - 1;
            sampledX.add(xData.get(lastIndex));
            sampledY1.add(lastIndex < yData1.size() ? yData1.get(lastIndex) : null);
            sampledY2.add(lastIndex < yData2.size() ? yData2.get(lastIndex) : null);
        }

        System.out.println("数据采样：原始数据点数 " + xData.size() + " -> 采样后数据点数 " + sampledX.size());

        return new DataSample(sampledX, sampledY1, sampledY2);
    }

    /**
     * 多曲线数据采样，保证数据一致性
     */
    private MultiCurveDataSample sampleMultiCurveData(List<String> xData, List<Double> yLoadData, Map<String, List<Double>> yWeatherDataMap) {
        final int MAX_POINTS = 3000; // 提高最大显示点数

        if (xData.size() <= MAX_POINTS) {
            // 数据量不大，不需要采样，但需要保证数据一致性
            Map<String, List<Double>> copiedWeatherData = new HashMap<>();
            for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                copiedWeatherData.put(entry.getKey(), new ArrayList<>(entry.getValue()));
            }
            return new MultiCurveDataSample(new ArrayList<>(xData), new ArrayList<>(yLoadData), copiedWeatherData);
        }

        // 计算采样间隔，使用更精细的采样策略
        int step = Math.max(2, xData.size() / MAX_POINTS);

        List<String> sampledX = new ArrayList<>();
        List<Double> sampledLoad = new ArrayList<>();
        Map<String, List<Double>> sampledWeatherData = new HashMap<>();

        // 初始化气象数据集合
        for (String curveName : yWeatherDataMap.keySet()) {
            sampledWeatherData.put(curveName, new ArrayList<>());
        }

        // 保留第一个数据点
        sampledX.add(xData.get(0));
        sampledLoad.add(yLoadData.size() > 0 ? yLoadData.get(0) : null);
        for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
            List<Double> curveData = entry.getValue();
            sampledWeatherData.get(entry.getKey()).add(curveData.size() > 0 ? curveData.get(0) : null);
        }

        // 采样中间数据点，确保均匀分布
        for (int i = step; i < xData.size() - 1; i += step) {
            sampledX.add(xData.get(i));
            sampledLoad.add(i < yLoadData.size() ? yLoadData.get(i) : null);
            for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                List<Double> curveData = entry.getValue();
                sampledWeatherData.get(entry.getKey()).add(i < curveData.size() ? curveData.get(i) : null);
            }
        }

        // 保留最后一个数据点
        if (xData.size() > 1) {
            int lastIndex = xData.size() - 1;
            sampledX.add(xData.get(lastIndex));
            sampledLoad.add(lastIndex < yLoadData.size() ? yLoadData.get(lastIndex) : null);
            for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                List<Double> curveData = entry.getValue();
                sampledWeatherData.get(entry.getKey()).add(lastIndex < curveData.size() ? curveData.get(lastIndex) : null);
            }
        }

        System.out.println("多曲线数据采样：原始数据点数 " + xData.size() + " -> 采样后数据点数 " + sampledX.size());

        return new MultiCurveDataSample(sampledX, sampledLoad, sampledWeatherData);
    }

    /**
     * 生成支持动态数据加载的单曲线HTML内容
     */
    private String generateDynamicHtmlContent(String dataKey, List<String> xData, List<Double> yData1, List<Double> yData2) {
        // 初始显示采样数据
        DataSample sample = sampleData(xData, yData1, yData2);

        List<String> sampledXData = sample.xData;
        List<Double> resultLoad = sample.yData1;
        List<Double> resultWeather = sample.yData2;

        // 确保数据长度一致
        while (resultLoad.size() < sampledXData.size()) {
            resultLoad.add(null);
        }
        while (resultWeather.size() < sampledXData.size()) {
            resultWeather.add(null);
        }

        String xDataStr = toJson(sampledXData);
        String yLoadDataStr = toJsonWithNull(resultLoad);
        String yWeatherDataStr = toJsonWithNull(resultWeather);

        // 计算范围
        RangeAndTickInterval yData1Range = calculateRangeAndTickInterval(yData1);
        RangeAndTickInterval yData2Range = calculateRangeAndTickInterval(yData2);

        String plotlyJsPath = Objects.requireNonNull(getClass().getResource("/js/plotly-latest.min.js")).toExternalForm();

        // 存储原始全量数据供动态加载使用
        String fullXDataStr = toJson(xData);
        String fullYLoadDataStr = toJsonWithNull(yData1);
        String fullYWeatherDataStr = toJsonWithNull(yData2);

        return generateDynamicPlotlyHtml(plotlyJsPath, dataKey, xDataStr, yLoadDataStr, yWeatherDataStr,
                                        yData1Range, yData2Range, false, null,
                                        fullXDataStr, fullYLoadDataStr, fullYWeatherDataStr, null);
    }

    /**
     * 生成支持动态数据加载的多曲线HTML内容
     */
    private String generateDynamicHtmlContentMultiCurve(String dataKey, List<String> xData, List<Double> yLoadData,
                                                        Map<String, List<Double>> yWeatherDataMap) {
        // 初始显示采样数据
        MultiCurveDataSample sample = sampleMultiCurveData(xData, yLoadData, yWeatherDataMap);

        List<String> sampledXData = sample.xData;
        List<Double> resultLoad = sample.yLoadData;
        Map<String, List<Double>> processedWeatherDataMap = sample.yWeatherDataMap;

        // 确保数据长度一致
        while (resultLoad.size() < sampledXData.size()) {
            resultLoad.add(null);
        }
        for (Map.Entry<String, List<Double>> entry : processedWeatherDataMap.entrySet()) {
            List<Double> curveData = entry.getValue();
            while (curveData.size() < sampledXData.size()) {
                curveData.add(null);
            }
        }

        String xDataStr = toJson(sampledXData);
        String yLoadDataStr = toJsonWithNull(resultLoad);

        // 计算范围
        RangeAndTickInterval yLoadRange = calculateRangeAndTickInterval(yLoadData);
        List<Double> allWeatherData = new ArrayList<>();
        for (List<Double> curveData : yWeatherDataMap.values()) {
            allWeatherData.addAll(curveData.stream().filter(Objects::nonNull).toList());
        }
        RangeAndTickInterval yWeatherRange = calculateRangeAndTickInterval(allWeatherData);

        String plotlyJsPath = Objects.requireNonNull(getClass().getResource("/js/plotly-latest.min.js")).toExternalForm();

        // 存储原始全量数据供动态加载使用
        String fullXDataStr = toJson(xData);
        String fullYLoadDataStr = toJsonWithNull(yLoadData);

        // 处理原始全量气象数据
        Map<String, String> fullWeatherDataJsonMap = new HashMap<>();
        for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
            fullWeatherDataJsonMap.put(entry.getKey(), toJsonWithNull(entry.getValue()));
        }

        return generateDynamicPlotlyHtml(plotlyJsPath, dataKey, xDataStr, yLoadDataStr, null,
                                        yLoadRange, yWeatherRange, true, processedWeatherDataMap,
                                        fullXDataStr, fullYLoadDataStr, null, fullWeatherDataJsonMap);
    }

    /**
     * 生成优化的单曲线HTML内容
     */
    private String generateOptimizedHtml(List<String> sampledXData,String plotlyJsPath, String xDataStr, String yLoadDataStr, String yWeatherDataStr,
                                        RangeAndTickInterval yData1Range, RangeAndTickInterval yData2Range, boolean isMultiCurve) {
        return "<!DOCTYPE html>\n" +
                "<html>\n" +
                "<head>\n" +
                String.format("    <script src=\"%s\"></script>\n", plotlyJsPath) +
                "    <style>\n" +
                "        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n" +
                "        #plot { width: 100%; height: 600px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }\n" +
                "        h1 { color: #333; text-align: center; margin-bottom: 20px; }\n" +
                "        .loading { text-align: center; padding: 50px; color: #666; }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <h1>负荷气象时序图</h1>\n" +
                "    <div id=\"plot\">\n" +
                "        <div class=\"loading\">加载中...</div>\n" +
                "    </div>\n" +
                "    <script>\n" +
                "        // 优化的Plotly配置\n" +
                "        if (typeof Plotly === 'undefined') {\n" +
                "            console.error('Plotly is not defined.');\n" +
                "            document.getElementById('plot').innerHTML = '<div class=\"loading\">加载失败</div>';\n" +
                "        } else {\n" +
                "            // 使用requestAnimationFrame优化渲染\n" +
                "            requestAnimationFrame(function() {\n" +
                "                // 添加隐藏的时间轴trace来确保所有时间点都能显示\n" +
                "                var trace_timeaxis = {\n" +
                String.format("                    x: %s,\n", xDataStr) +
                String.format("                    y: %s,\n", toJsonWithNull(Collections.nCopies(sampledXData.size(), 0.0))) +
                "                    mode: 'markers',\n" +
                "                    marker: { size: 0, opacity: 0 },\n" +
                "                    name: '',\n" +
                "                    yaxis: 'y1',\n" +
                "                    hovertemplate: '时间: %{x}<extra></extra>',\n" +
                "                    showlegend: false,\n" +
                "                    visible: true\n" +
                "                };\n" +
                "\n" +
                "                var trace1 = {\n" +
                String.format("                    x: %s,\n", xDataStr) +
                String.format("                    y: %s,\n", yLoadDataStr) +
                "                    mode: 'lines+markers',\n" +
                "                    line: { color: '#2C82C9', width: 1.5 },\n" +
                "                    marker: { size: 3, color: '#2C82C9' },\n" +
                "                    name: '负荷',\n" +
                "                    yaxis: 'y1',\n" +
                "                    hovertemplate: '<b>负荷</b><br>时间: %{x}<br>数值: %{y}<extra></extra>',\n" +
                "                    connectgaps: false\n" +
                "                };\n" +
                "\n" +
                "                var trace2 = {\n" +
                String.format("                    x: %s,\n", xDataStr) +
                String.format("                    y: %s,\n", yWeatherDataStr) +
                "                    mode: 'lines+markers',\n" +
                "                    line: { color: '#EF4836', width: 1.5 },\n" +
                "                    marker: { size: 3, color: '#EF4836' },\n" +
                "                    name: '温度',\n" +
                "                    yaxis: 'y2',\n" +
                "                    hovertemplate: '<b>温度</b><br>时间: %{x}<br>数值: %{y}<extra></extra>',\n" +
                "                    connectgaps: false\n" +
                "                };\n" +
                "\n" +
                "                var data = [trace_timeaxis, trace1, trace2];\n" +
                "\n" +
                "                var layout = {\n" +
                "                    title: { text: '负荷与气象数据对比', font: { size: 20 } },\n" +
                "                    xaxis: {\n" +
                "                        title: { text: '时间', font: { size: 14 } },\n" +
                "                        type: 'category',\n" +
                "                        tickmode: 'auto',\n" +
                "                        tickangle: -45,\n" +
                "                        automargin: true,\n" +
                "                        showgrid: true,\n" +
                "                        gridcolor: '#e0e0e0',\n" +
                "                        rangeslider: { visible: true, thickness: 0.1 }\n" +
                "                    },\n" +
                "                    yaxis: {\n" +
                "                        title: { text: '负荷值', font: { size: 14, color: '#2C82C9' } },\n" +
                "                        side: 'left',\n" +
                String.format("                        range: [%.2f, %.2f],\n", yData1Range.min, yData1Range.max) +
                String.format("                        dtick: %.2f,\n", yData1Range.tickInterval) +
                "                        tickfont: { color: '#2C82C9' }\n" +
                "                    },\n" +
                "                    yaxis2: {\n" +
                "                        title: { text: '温度值', font: { size: 14, color: '#EF4836' } },\n" +
                "                        side: 'right',\n" +
                "                        overlaying: 'y',\n" +
                String.format("                        range: [%.2f, %.2f],\n", yData2Range.min, yData2Range.max) +
                String.format("                        dtick: %.2f,\n", yData2Range.tickInterval) +
                "                        tickfont: { color: '#EF4836' }\n" +
                "                    },\n" +
                "                    margin: { l: 60, r: 80, t: 60, b: 80 },\n" +
                "                    hovermode: 'x unified',\n" +
                "                    hoverdistance: 50,\n" +
                "                    legend: { x: 1.02, y: 1, xanchor: 'left', yanchor: 'top' },\n" +
                "                    dragmode: 'zoom'\n" +
                "                };\n" +
                "\n" +
                "                // 优化的配置选项\n" +
                "                var config = {\n" +
                "                    responsive: true,\n" +
                "                    displayModeBar: true,\n" +
                "                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],\n" +
                "                    doubleClick: 'reset+autosize'\n" +
                "                };\n" +
                "\n" +
                "                Plotly.newPlot('plot', data, layout, config);\n" +
                "            });\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * 生成优化的多曲线HTML内容
     */
    private String generateOptimizedMultiCurveHtml(List<String> sampledXData,String plotlyJsPath, String xDataStr, String yLoadDataStr,
                                                  Map<String, List<Double>> processedWeatherDataMap,
                                                  RangeAndTickInterval yLoadRange, RangeAndTickInterval yWeatherRange) {
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<!DOCTYPE html>\n")
                .append("<html>\n")
                .append("<head>\n")
                .append(String.format("    <script src=\"%s\"></script>\n", plotlyJsPath))
                .append("    <style>\n")
                .append("        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n")
                .append("        #plot { width: 100%; height: 600px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }\n")
                .append("        h1 { color: #333; text-align: center; margin-bottom: 20px; }\n")
                .append("        .loading { text-align: center; padding: 50px; color: #666; }\n")
                .append("    </style>\n")
                .append("</head>\n")
                .append("<body>\n")
                .append("    <h1>负荷气象时序图（多曲线）</h1>\n")
                .append("    <div id=\"plot\">\n")
                .append("        <div class=\"loading\">加载中...</div>\n")
                .append("    </div>\n")
                .append("    <script>\n")
                .append("        if (typeof Plotly === 'undefined') {\n")
                .append("            console.error('Plotly is not defined.');\n")
                .append("            document.getElementById('plot').innerHTML = '<div class=\"loading\">加载失败</div>';\n")
                .append("        } else {\n")
                .append("            requestAnimationFrame(function() {\n");

        // 添加隐藏的时间轴trace来确保所有时间点都能显示
        htmlBuilder.append("                var trace_timeaxis = {\n")
                .append(String.format("                    x: %s,\n", xDataStr))
                .append(String.format("                    y: %s,\n", toJsonWithNull(Collections.nCopies(sampledXData.size(), 0.0))))
                .append("                    mode: 'markers',\n")
                .append("                    marker: { size: 0, opacity: 0 },\n")
                .append("                    name: '',\n")
                .append("                    yaxis: 'y1',\n")
                .append("                    hovertemplate: '时间: %{x}<extra></extra>',\n")
                .append("                    showlegend: false,\n")
                .append("                    visible: true\n")
                .append("                };\n\n");

        // 添加负荷曲线
        htmlBuilder.append("                var trace_load = {\n")
                .append(String.format("                    x: %s,\n", xDataStr))
                .append(String.format("                    y: %s,\n", yLoadDataStr))
                .append("                    mode: 'lines+markers',\n")
                .append("                    line: { color: '#2C82C9', width: 1.5 },\n")
                .append("                    marker: { size: 3, color: '#2C82C9' },\n")
                .append("                    name: '负荷',\n")
                .append("                    yaxis: 'y1',\n")
                .append("                    hovertemplate: '<b>负荷</b><br>时间: %{x}<br>数值: %{y}<extra></extra>',\n")
                .append("                    connectgaps: false\n")
                .append("                };\n\n");

        // 添加气象曲线
        String[] colors = {"#EF4836", "#28A745", "#FFC107", "#6F42C1", "#FD7E14", "#20C997", "#E83E8C", "#6C757D"};
        int colorIndex = 0;

        for (Map.Entry<String, List<Double>> entry : processedWeatherDataMap.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            String curveDataStr = toJsonWithNull(curveData);
            String color = colors[colorIndex % colors.length];

            htmlBuilder.append(String.format("                var trace_%s = {\n", curveName.replaceAll("[^a-zA-Z0-9]", "_")))
                    .append(String.format("                    x: %s,\n", xDataStr))
                    .append(String.format("                    y: %s,\n", curveDataStr))
                    .append("                    mode: 'lines+markers',\n")
                    .append(String.format("                    line: { color: '%s', width: 1.5 },\n", color))
                    .append(String.format("                    marker: { size: 3, color: '%s' },\n", color))
                    .append(String.format("                    name: '%s',\n", curveName))
                    .append("                    yaxis: 'y2',\n")
                    .append(String.format("                    hovertemplate: '<b>%s</b><br>时间: %%{x}<br>数值: %%{y}<extra></extra>',\n", curveName))
                    .append("                    connectgaps: false\n")
                    .append("                };\n\n");

            colorIndex++;
        }

        // 添加数据数组，确保包含时间轴trace
        htmlBuilder.append("                var data = [trace_timeaxis, trace_load");
        for (String curveName : processedWeatherDataMap.keySet()) {
            htmlBuilder.append(", trace_").append(curveName.replaceAll("[^a-zA-Z0-9]", "_"));
        }
        htmlBuilder.append("];\n\n");

        // 添加布局配置
        htmlBuilder.append("                var layout = {\n")
                .append("                    title: { text: '负荷气象时序图（多曲线）', font: { size: 20 } },\n")
                .append("                    xaxis: {\n")
                .append("                        title: { text: '时间', font: { size: 14 } },\n")
                .append("                        type: 'category',\n")
                .append("                        tickmode: 'auto',\n")
                .append("                        tickangle: -45,\n")
                .append("                        automargin: true,\n")
                .append("                        showgrid: true,\n")
                .append("                        gridcolor: '#e0e0e0',\n")
                .append("                        rangeslider: { visible: true, thickness: 0.1 }\n")
                .append("                    },\n")
                .append("                    yaxis: {\n")
                .append("                        title: { text: '负荷值', font: { size: 14, color: '#2C82C9' } },\n")
                .append("                        side: 'left',\n")
                .append(String.format("                        range: [%.2f, %.2f],\n", yLoadRange.min, yLoadRange.max))
                .append(String.format("                        dtick: %.2f\n", yLoadRange.tickInterval))
                .append("                    },\n")
                .append("                    yaxis2: {\n")
                .append("                        title: { text: '气象值', font: { size: 14 } },\n")
                .append("                        side: 'right',\n")
                .append("                        overlaying: 'y',\n")
                .append(String.format("                        range: [%.2f, %.2f],\n", yWeatherRange.min, yWeatherRange.max))
                .append(String.format("                        dtick: %.2f\n", yWeatherRange.tickInterval))
                .append("                    },\n")
                .append("                    legend: {\n")
                .append("                        x: 1.02,\n")
                .append("                        y: 1,\n")
                .append("                        xanchor: 'left',\n")
                .append("                        yanchor: 'top'\n")
                .append("                    },\n")
                .append("                    hovermode: 'x unified',\n")
                .append("                    hoverdistance: 50,\n")
                .append("                    margin: { l: 60, r: 120, t: 60, b: 80 },\n")
                .append("                    dragmode: 'zoom'\n")
                .append("                };\n\n")
                .append("                var config = {\n")
                .append("                    responsive: true,\n")
                .append("                    displayModeBar: true,\n")
                .append("                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],\n")
                .append("                    doubleClick: 'reset+autosize'\n")
                .append("                };\n\n")
                .append("                Plotly.newPlot('plot', data, layout, config);\n")
                .append("            });\n")
                .append("        }\n")
                .append("    </script>\n")
                .append("</body>\n")
                .append("</html>");

        return htmlBuilder.toString();
    }

    /**
     * 生成支持动态数据加载的Plotly HTML
     */
    private String generateDynamicPlotlyHtml(String plotlyJsPath, String dataKey, String xDataStr, String yLoadDataStr,
                                            String yWeatherDataStr, RangeAndTickInterval yData1Range,
                                            RangeAndTickInterval yData2Range, boolean isMultiCurve,
                                            Map<String, List<Double>> weatherDataMap,
                                            String fullXDataStr, String fullYLoadDataStr, String fullYWeatherDataStr,
                                            Map<String, String> fullWeatherDataJsonMap) {
        StringBuilder htmlBuilder = new StringBuilder();
        htmlBuilder.append("<!DOCTYPE html>\n")
                .append("<html>\n")
                .append("<head>\n")
                .append(String.format("    <script src=\"%s\"></script>\n", plotlyJsPath))
                .append("    <style>\n")
                .append("        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }\n")
                .append("        #plot { width: 100%; height: 600px; background-color: white; box-shadow: 0 2px 10px rgba(0,0,0,0.1); border-radius: 5px; }\n")
                .append("        h1 { color: #333; text-align: center; margin-bottom: 20px; }\n")
                .append("        .loading { text-align: center; padding: 50px; color: #666; }\n")
                .append("        .zoom-info { position: absolute; top: 10px; right: 10px; background: rgba(255,255,255,0.9); padding: 10px; border-radius: 5px; font-size: 12px; }\n")
                .append("    </style>\n")
                .append("</head>\n")
                .append("<body>\n")
                .append("    <h1>").append(isMultiCurve ? "负荷气象时序图（多曲线）" : "负荷气象时序图").append("</h1>\n")
                .append("    <div class=\"zoom-info\" id=\"zoomInfo\">整体视图（采样数据）</div>\n")
                .append("    <div id=\"plot\">\n")
                .append("        <div class=\"loading\">加载中...</div>\n")
                .append("    </div>\n")
                .append("    <script>\n")
                .append("        // 全局变量\n")
                .append(String.format("        var dataKey = '%s';\n", dataKey))
                .append("        var isZoomed = false;\n")
                .append("        var currentXRange = null;\n")
                .append("        var originalData = null;\n")
                .append("        var zoomThreshold = 0.1; // 缩放阈值，小于10%的范围时加载全量数据\n")
                .append("        \n")
                .append("        // 存储原始全量数据\n")
                .append(String.format("        var fullXData = %s;\n", fullXDataStr))
                .append(String.format("        var fullYLoadData = %s;\n", fullYLoadDataStr));

        if (isMultiCurve && fullWeatherDataJsonMap != null) {
            htmlBuilder.append("        var fullYWeatherDataMap = {\n");
            boolean first = true;
            for (Map.Entry<String, String> entry : fullWeatherDataJsonMap.entrySet()) {
                if (!first) htmlBuilder.append(",\n");
                htmlBuilder.append(String.format("            '%s': %s", entry.getKey(), entry.getValue()));
                first = false;
            }
            htmlBuilder.append("\n        };\n");
        } else if (!isMultiCurve && fullYWeatherDataStr != null) {
            htmlBuilder.append(String.format("        var fullYWeatherData = %s;\n", fullYWeatherDataStr));
        }

        htmlBuilder.append("        \n")
                .append("        // 存储全量数据到全局变量供动态加载使用\n")
                .append("        window.fullOriginalData = null; // 将在初始化时设置\n");

        // 添加JavaFX Bridge
        htmlBuilder.append("        \n")
                .append("        // JavaFX Bridge 用于与Java通信\n")
                .append("        window.javaConnector = {\n")
                .append("            getFullData: function(dataKey, startRange, endRange) {\n")
                .append("                try {\n")
                .append("                    var startIndex = Math.max(0, Math.floor(startRange));\n")
                .append("                    var endIndex = Math.min(fullXData.length - 1, Math.ceil(endRange));\n")
                .append("                    \n")
                .append("                    var result = {\n")
                .append("                        success: true,\n")
                .append("                        xData: fullXData.slice(startIndex, endIndex + 1),\n")
                .append("                        yLoadData: fullYLoadData.slice(startIndex, endIndex + 1),\n")
                .append("                        totalPoints: endIndex - startIndex + 1,\n")
                .append("                        startIndex: startIndex,\n")
                .append("                        endIndex: endIndex\n")
                .append("                    };\n")
                .append("                    \n");

        if (isMultiCurve) {
            htmlBuilder.append("                    result.isMultiCurve = true;\n")
                    .append("                    result.yWeatherDataMap = {};\n")
                    .append("                    for (var curveName in fullYWeatherDataMap) {\n")
                    .append("                        result.yWeatherDataMap[curveName] = fullYWeatherDataMap[curveName].slice(startIndex, endIndex + 1);\n")
                    .append("                    }\n");
        } else {
            htmlBuilder.append("                    result.isMultiCurve = false;\n")
                    .append("                    result.yWeatherData = fullYWeatherData.slice(startIndex, endIndex + 1);\n");
        }

        htmlBuilder.append("                    \n")
                .append("                    return result;\n")
                .append("                } catch (error) {\n")
                .append("                    console.error('Error in JavaFX Bridge:', error);\n")
                .append("                    return { success: false, error: error.message };\n")
                .append("                }\n")
                .append("            }\n")
                .append("        };\n\n")
                .append("        if (typeof Plotly === 'undefined') {\n")
                .append("            console.error('Plotly is not defined.');\n")
                .append("            document.getElementById('plot').innerHTML = '<div class=\"loading\">加载失败</div>';\n")
                .append("        } else {\n")
                .append("            initializePlot();\n")
                .append("        }\n")
                .append("\n")
                .append("        function initializePlot() {\n")
                .append("            requestAnimationFrame(function() {\n");

        // 添加初始数据
        if (isMultiCurve) {
            addMultiCurveTraces(htmlBuilder, xDataStr, yLoadDataStr, weatherDataMap);
        } else {
            addSingleCurveTraces(htmlBuilder, xDataStr, yLoadDataStr, yWeatherDataStr);
        }

        // 添加布局和事件处理
        addLayoutAndEvents(htmlBuilder, yData1Range, yData2Range, isMultiCurve);

        htmlBuilder.append("            });\n")
                .append("        }\n")
                .append("\n");

        // 添加数据加载函数
        addDataLoadingFunctions(htmlBuilder, dataKey, isMultiCurve);

        htmlBuilder.append("    </script>\n")
                .append("</body>\n")
                .append("</html>");

        return htmlBuilder.toString();
    }

    /**
     * 添加单曲线迹踪
     */
    private void addSingleCurveTraces(StringBuilder htmlBuilder, String xDataStr, String yLoadDataStr, String yWeatherDataStr) {
        htmlBuilder.append("                // 添加隐藏的时间轴trace\n")
                .append("                var trace_timeaxis = {\n")
                .append(String.format("                    x: %s,\n", xDataStr))
                .append(String.format("                    y: %s,\n", toJsonWithNull(Collections.nCopies(xDataStr.split(",").length, 0.0))))
                .append("                    mode: 'markers',\n")
                .append("                    marker: { size: 0, opacity: 0 },\n")
                .append("                    name: '',\n")
                .append("                    yaxis: 'y1',\n")
                .append("                    hovertemplate: '时间: %{x}<extra></extra>',\n")
                .append("                    showlegend: false,\n")
                .append("                    visible: true\n")
                .append("                };\n\n")
                .append("                var trace1 = {\n")
                .append(String.format("                    x: %s,\n", xDataStr))
                .append(String.format("                    y: %s,\n", yLoadDataStr))
                .append("                    mode: 'lines+markers',\n")
                .append("                    line: { color: '#2C82C9', width: 1.5 },\n")
                .append("                    marker: { size: 3, color: '#2C82C9' },\n")
                .append("                    name: '负荷',\n")
                .append("                    yaxis: 'y1',\n")
                .append("                    hovertemplate: '<b>负荷</b><br>时间: %{x}<br>数值: %{y}<extra></extra>',\n")
                .append("                    connectgaps: false\n")
                .append("                };\n\n")
                .append("                var trace2 = {\n")
                .append(String.format("                    x: %s,\n", xDataStr))
                .append(String.format("                    y: %s,\n", yWeatherDataStr))
                .append("                    mode: 'lines+markers',\n")
                .append("                    line: { color: '#EF4836', width: 1.5 },\n")
                .append("                    marker: { size: 3, color: '#EF4836' },\n")
                .append("                    name: '温度',\n")
                .append("                    yaxis: 'y2',\n")
                .append("                    hovertemplate: '<b>温度</b><br>时间: %{x}<br>数值: %{y}<extra></extra>',\n")
                .append("                    connectgaps: false\n")
                .append("                };\n\n")
                .append("                originalData = [trace_timeaxis, trace1, trace2];\n");
    }

    /**
     * 添加多曲线迹踪
     */
    private void addMultiCurveTraces(StringBuilder htmlBuilder, String xDataStr, String yLoadDataStr,
                                   Map<String, List<Double>> weatherDataMap) {
        htmlBuilder.append("                // 添加隐藏的时间轴trace\n")
                .append("                var trace_timeaxis = {\n")
                .append(String.format("                    x: %s,\n", xDataStr))
                .append(String.format("                    y: %s,\n", toJsonWithNull(Collections.nCopies(xDataStr.split(",").length, 0.0))))
                .append("                    mode: 'markers',\n")
                .append("                    marker: { size: 0, opacity: 0 },\n")
                .append("                    name: '',\n")
                .append("                    yaxis: 'y1',\n")
                .append("                    hovertemplate: '时间: %{x}<extra></extra>',\n")
                .append("                    showlegend: false,\n")
                .append("                    visible: true\n")
                .append("                };\n\n")
                .append("                var trace_load = {\n")
                .append(String.format("                    x: %s,\n", xDataStr))
                .append(String.format("                    y: %s,\n", yLoadDataStr))
                .append("                    mode: 'lines+markers',\n")
                .append("                    line: { color: '#2C82C9', width: 1.5 },\n")
                .append("                    marker: { size: 3, color: '#2C82C9' },\n")
                .append("                    name: '负荷',\n")
                .append("                    yaxis: 'y1',\n")
                .append("                    hovertemplate: '<b>负荷</b><br>时间: %{x}<br>数值: %{y}<extra></extra>',\n")
                .append("                    connectgaps: false\n")
                .append("                };\n\n");

        // 添加气象曲线
        String[] colors = {"#EF4836", "#28A745", "#FFC107", "#6F42C1", "#FD7E14", "#20C997", "#E83E8C", "#6C757D"};
        int colorIndex = 0;

        for (Map.Entry<String, List<Double>> entry : weatherDataMap.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            String curveDataStr = toJsonWithNull(curveData);
            String color = colors[colorIndex % colors.length];

            htmlBuilder.append(String.format("                var trace_%s = {\n", curveName.replaceAll("[^a-zA-Z0-9]", "_")))
                    .append(String.format("                    x: %s,\n", xDataStr))
                    .append(String.format("                    y: %s,\n", curveDataStr))
                    .append("                    mode: 'lines+markers',\n")
                    .append(String.format("                    line: { color: '%s', width: 1.5 },\n", color))
                    .append(String.format("                    marker: { size: 3, color: '%s' },\n", color))
                    .append(String.format("                    name: '%s',\n", curveName))
                    .append("                    yaxis: 'y2',\n")
                    .append(String.format("                    hovertemplate: '<b>%s</b><br>时间: %%{x}<br>数值: %%{y}<extra></extra>',\n", curveName))
                    .append("                    connectgaps: false\n")
                    .append("                };\n\n");

            colorIndex++;
        }

        // 添加数据数组
        htmlBuilder.append("                originalData = [trace_timeaxis, trace_load");
        for (String curveName : weatherDataMap.keySet()) {
            htmlBuilder.append(", trace_").append(curveName.replaceAll("[^a-zA-Z0-9]", "_"));
        }
        htmlBuilder.append("];\n");
    }

    /**
     * 添加布局和事件处理
     */
    private void addLayoutAndEvents(StringBuilder htmlBuilder, RangeAndTickInterval yData1Range,
                                  RangeAndTickInterval yData2Range, boolean isMultiCurve) {
        htmlBuilder.append("\n                var layout = {\n")
                .append("                    title: { text: '").append(isMultiCurve ? "负荷气象时序图（多曲线）" : "负荷与气象数据对比").append("', font: { size: 20 } },\n")
                .append("                    xaxis: {\n")
                .append("                        title: { text: '时间', font: { size: 14 } },\n")
                .append("                        type: 'category',\n")
                .append("                        tickmode: 'auto',\n")
                .append("                        tickangle: -45,\n")
                .append("                        automargin: true,\n")
                .append("                        showgrid: true,\n")
                .append("                        gridcolor: '#e0e0e0',\n")
                .append("                        rangeslider: { visible: true, thickness: 0.1 }\n")
                .append("                    },\n")
                .append("                    yaxis: {\n")
                .append("                        title: { text: '负荷值', font: { size: 14, color: '#2C82C9' } },\n")
                .append("                        side: 'left',\n")
                .append(String.format("                        range: [%.2f, %.2f],\n", yData1Range.min, yData1Range.max))
                .append(String.format("                        dtick: %.2f,\n", yData1Range.tickInterval))
                .append("                        tickfont: { color: '#2C82C9' }\n")
                .append("                    },\n")
                .append("                    yaxis2: {\n")
                .append("                        title: { text: '").append(isMultiCurve ? "气象值" : "温度值").append("', font: { size: 14, color: '#EF4836' } },\n")
                .append("                        side: 'right',\n")
                .append("                        overlaying: 'y',\n")
                .append(String.format("                        range: [%.2f, %.2f],\n", yData2Range.min, yData2Range.max))
                .append(String.format("                        dtick: %.2f,\n", yData2Range.tickInterval))
                .append("                        tickfont: { color: '#EF4836' }\n")
                .append("                    },\n")
                .append("                    margin: { l: 60, r: ").append(isMultiCurve ? "120" : "80").append(", t: 60, b: 80 },\n")
                .append("                    hovermode: 'x unified',\n")
                .append("                    hoverdistance: 50,\n")
                .append("                    legend: { x: 1.02, y: 1, xanchor: 'left', yanchor: 'top' },\n")
                .append("                    dragmode: 'zoom'\n")
                .append("                };\n\n")
                .append("                var config = {\n")
                .append("                    responsive: true,\n")
                .append("                    displayModeBar: true,\n")
                .append("                    modeBarButtonsToRemove: ['pan2d', 'lasso2d', 'select2d'],\n")
                .append("                    doubleClick: 'reset+autosize'\n")
                .append("                };\n\n")
                .append("                Plotly.newPlot('plot', originalData, layout, config);\n\n")
                .append("                // 添加缩放事件监听\n")
                .append("                document.getElementById('plot').on('plotly_relayout', function(eventData) {\n")
                .append("                    handleZoomEvent(eventData);\n")
                .append("                });\n");
    }

    /**
     * 添加数据加载函数
     */
    private void addDataLoadingFunctions(StringBuilder htmlBuilder, String dataKey, boolean isMultiCurve) {
        htmlBuilder.append("\n        // 处理缩放事件\n")
                .append("        function handleZoomEvent(eventData) {\n")
                .append("            console.log('Zoom event:', eventData);\n")
                .append("            \n")
                .append("            if (eventData['xaxis.range[0]'] && eventData['xaxis.range[1]']) {\n")
                .append("                var startRange = eventData['xaxis.range[0]'];\n")
                .append("                var endRange = eventData['xaxis.range[1]'];\n")
                .append("                \n")
                .append("                // 计算缩放比例\n")
                .append("                var totalDataPoints = originalData[1].x.length; // 使用第一个数据系列\n")
                .append("                var zoomRatio = (endRange - startRange) / totalDataPoints;\n")
                .append("                \n")
                .append("                console.log('Zoom ratio:', zoomRatio, 'Threshold:', zoomThreshold);\n")
                .append("                \n")
                .append("                if (zoomRatio < zoomThreshold && !isZoomed) {\n")
                .append("                    // 缩放比例小于阈值，加载全量数据\n")
                .append("                    loadDetailedData(startRange, endRange);\n")
                .append("                } else if (zoomRatio >= zoomThreshold && isZoomed) {\n")
                .append("                    // 缩放比例大于阈值，恢复采样数据\n")
                .append("                    restoreSampledData();\n")
                .append("                }\n")
                .append("            } else if (eventData['xaxis.autorange'] || eventData.autosize) {\n")
                .append("                // 自动调整或重置，恢复采样数据\n")
                .append("                if (isZoomed) {\n")
                .append("                    restoreSampledData();\n")
                .append("                }\n")
                .append("            }\n")
                .append("        }\n\n")
                .append("        // 加载详细数据\n")
                .append("        function loadDetailedData(startRange, endRange) {\n")
                .append("            console.log('Loading detailed data for range:', startRange, 'to', endRange);\n")
                .append("            \n")
                .append("            // 更新状态显示\n")
                .append("            document.getElementById('zoomInfo').innerHTML = '细节视图（全量数据） - 加载中...';\n")
                .append("            \n")
                .append("            // 直接使用本地全量数据\n")
                .append("            setTimeout(function() {\n")
                .append("                try {\n")
                .append("                    var detailedData = generateDetailedDataFromOriginal(startRange, endRange);\n")
                .append("                    \n")
                .append("                    if (detailedData && detailedData.length > 0) {\n")
                .append("                        Plotly.react('plot', detailedData);\n")
                .append("                        isZoomed = true;\n")
                .append("                        currentXRange = [startRange, endRange];\n")
                .append("                        document.getElementById('zoomInfo').innerHTML = '细节视图（全量数据）';\n")
                .append("                        console.log('Detailed data loaded successfully');\n")
                .append("                    } else {\n")
                .append("                        console.error('Failed to load detailed data');\n")
                .append("                        document.getElementById('zoomInfo').innerHTML = '细节视图 - 加载失败';\n")
                .append("                    }\n")
                .append("                } catch (error) {\n")
                .append("                    console.error('Error loading detailed data:', error);\n")
                .append("                    document.getElementById('zoomInfo').innerHTML = '细节视图 - 加载错误';\n")
                .append("                }\n")
                .append("            }, 100); // 短暂延迟以显示加载状态\n")
                .append("        }\n\n")
                .append("        // 恢复采样数据\n")
                .append("        function restoreSampledData() {\n")
                .append("            console.log('Restoring sampled data');\n")
                .append("            Plotly.react('plot', originalData);\n")
                .append("            isZoomed = false;\n")
                .append("            currentXRange = null;\n")
                .append("            document.getElementById('zoomInfo').innerHTML = '整体视图（采样数据）';\n")
                .append("        }\n\n")
                .append("        // 生成详细数据（使用原始全量数据）\n")
                .append("        function generateDetailedData(startRange, endRange) {\n")
                .append("            try {\n")
                .append("                // 通过JavaFX Bridge获取原始全量数据\n")
                .append("                var fullData = window.javaConnector.getFullData(dataKey, startRange, endRange);\n")
                .append("                \n")
                .append("                if (fullData && fullData.success) {\n")
                .append("                    // 使用从Java获取的全量数据\n")
                .append("                    return buildDetailedTraces(fullData);\n")
                .append("                } else {\n")
                .append("                    // 如果JavaFX Bridge不可用，使用本地原始数据\n")
                .append("                    return generateLocalDetailedData(startRange, endRange);\n")
                .append("                }\n")
                .append("            } catch (error) {\n")
                .append("                console.error('Error accessing Java bridge, using local data:', error);\n")
                .append("                return generateLocalDetailedData(startRange, endRange);\n")
                .append("            }\n")
                .append("        }\n\n")
                .append("        // 构建详细数据traces\n")
                .append("        function buildDetailedTraces(fullData) {\n")
                .append("            var detailedTraces = JSON.parse(JSON.stringify(originalData));\n")
                .append("            \n")
                .append("            // 更新数据\n")
                .append("            detailedTraces[0].x = fullData.xData;\n")
                .append("            detailedTraces[0].y = new Array(fullData.xData.length).fill(0);\n")
                .append("            detailedTraces[1].x = fullData.xData;\n")
                .append("            detailedTraces[1].y = fullData.yLoadData;\n")
                .append("            \n")
                .append("            if (fullData.isMultiCurve && fullData.yWeatherDataMap) {\n")
                .append("                var traceIndex = 2;\n")
                .append("                for (var curveName in fullData.yWeatherDataMap) {\n")
                .append("                    if (detailedTraces[traceIndex]) {\n")
                .append("                        detailedTraces[traceIndex].x = fullData.xData;\n")
                .append("                        detailedTraces[traceIndex].y = fullData.yWeatherDataMap[curveName];\n")
                .append("                        traceIndex++;\n")
                .append("                    }\n")
                .append("                }\n")
                .append("            } else if (fullData.yWeatherData) {\n")
                .append("                detailedTraces[2].x = fullData.xData;\n")
                .append("                detailedTraces[2].y = fullData.yWeatherData;\n")
                .append("            }\n")
                .append("            \n")
                .append("            console.log(`加载了 ${fullData.totalPoints} 个详细数据点`);\n")
                .append("            return detailedTraces;\n")
                .append("        }\n\n")
                .append("        // 本地详细数据生成（备选方案）\n")
                .append("        function generateLocalDetailedData(startRange, endRange) {\n")
                .append("            try {\n")
                .append("                // 从全局存储的原始数据中获取\n")
                .append("                var fullOriginalData = window.fullOriginalData || originalData;\n")
                .append("                var originalXData = fullOriginalData[1].x;\n")
                .append("                var startIndex = Math.max(0, Math.floor(startRange));\n")
                .append("                var endIndex = Math.min(originalXData.length - 1, Math.ceil(endRange));\n")
                .append("                \n")
                .append("                var detailedXData = [];\n")
                .append("                var detailedYData1 = [];\n")
                .append("                var detailedYData2 = [];\n")
                .append("                \n")
                .append("                // 提取指定范围的原始数据，不进行采样\n")
                .append("                for (var i = startIndex; i <= endIndex; i++) {\n")
                .append("                    if (i < originalXData.length) {\n")
                .append("                        detailedXData.push(originalXData[i]);\n")
                .append("                        detailedYData1.push(fullOriginalData[1].y[i]);\n")
                .append("                        if (fullOriginalData[2]) {\n")
                .append("                            detailedYData2.push(fullOriginalData[2].y[i]);\n")
                .append("                        }\n")
                .append("                    }\n")
                .append("                }\n")
                .append("                \n")
                .append("                var detailedTraces = JSON.parse(JSON.stringify(originalData));\n")
                .append("                detailedTraces[0].x = detailedXData;\n")
                .append("                detailedTraces[0].y = new Array(detailedXData.length).fill(0);\n")
                .append("                detailedTraces[1].x = detailedXData;\n")
                .append("                detailedTraces[1].y = detailedYData1;\n")
                .append("                if (detailedTraces[2]) {\n")
                .append("                    detailedTraces[2].x = detailedXData;\n")
                .append("                    detailedTraces[2].y = detailedYData2;\n")
                .append("                }\n")
                .append("                \n")
                .append("                console.log(`本地加载了 ${detailedXData.length} 个详细数据点`);\n")
                .append("                return detailedTraces;\n")
                .append("            } catch (error) {\n")
                .append("                console.error('Error generating local detailed data:', error);\n")
                .append("                return null;\n")
                .append("            }\n")
                .append("        }\n");
    }

    // 截断文本的方法
    private String truncateText(String text, int length) {
        if (text.length() > length) {
            return text.substring(0, length) + "...";
        }
        return text;
    }


    public void handleExportSinge(ActionEvent event) {
        Map<String, List> stringListMap = fileExcelDataMap.get(comboBox.getValue());
        FileChooser fileChooser = new FileChooser();
        fileChooser.getExtensionFilters().add(
                new FileChooser.ExtensionFilter("Excel Files", "*.xlsx")
        );
        File file = fileChooser.showSaveDialog(null);

        if (file != null) {
            try (Workbook workbook = new HSSFWorkbook()) {
                mergeExcelData(stringListMap, workbook);
                try (FileOutputStream outputStream = new FileOutputStream(file)) {
                    workbook.write(outputStream);
                }

                System.out.println("Excel file has been created successfully.");
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private void mergeExcelData(Map<String, List> stringListMap, Workbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        setBorderStyle(cellStyle);
        for (Map.Entry<String, List> stringListEntry : stringListMap.entrySet()) {
            String sheetValue = stringListEntry.getKey();
            List value = stringListEntry.getValue();
            Sheet sheet = workbook.createSheet(sheetValue);

            for (int i = 0; i < value.size(); i++) {
                Row row = sheet.createRow(i);
                String[] rowData = (String[]) value.get(i);
                for (int j = 0; j < rowData.length; j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellValue(rowData[j]);
                    cell.setCellStyle(cellStyle);
                }
            }
        }
    }

    private static void setBorderStyle(CellStyle cellStyle) {
        BorderStyle border = BorderStyle.THIN;
        cellStyle.setBorderTop(border);
        cellStyle.setBorderBottom(border);
        cellStyle.setBorderLeft(border);
        cellStyle.setBorderRight(border);
    }

    public void handleExport(ActionEvent event) {
        try {

            if (fileExcelDataMap.size() ==1){
                handleExportSinge(event);
                return;
            }
            // 1. 创建临时文件夹
            File tempDir = createTempDirectory();

            // 2. 生成多个Excel文件
            generateExcelFiles(tempDir);

            // 3. 打包成ZIP
            File zipFile = packToZip(tempDir);

            // 4. 保存对话框
            saveZipFile(zipFile);

            // 5. 清理临时文件
            deleteTempFiles(tempDir);

        } catch (IOException ex) {
            ex.printStackTrace();
            // 这里可以添加错误提示弹窗
        }
    }

    private File createTempDirectory() throws IOException {
        File tempDir = File.createTempFile("excelExport", Long.toString(System.nanoTime()));
        tempDir.delete();
        tempDir.mkdir();
        return tempDir;
    }

    private void generateExcelFiles(File dir) throws IOException {

        for (Map.Entry<String, Map<String, List>> entry : fileExcelDataMap.entrySet()) {
            String directoryName = entry.getKey();
            Map<String, List> stringListMap = entry.getValue();
            try (Workbook workbook = new HSSFWorkbook()) {
                // 创建一个带有边框的单元格样式
                mergeExcelData(stringListMap, workbook);
                try (FileOutputStream outputStream = new FileOutputStream(new File(dir,
                        directoryName.split("/")[directoryName.split("/").length-1].replace(".e","") + ".xlsx"))) {
                    workbook.write(outputStream);
                }

                System.out.println("Excel file has been created successfully.");
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
    }
    private File packToZip(File sourceDir) throws IOException {
        File zipFile = File.createTempFile("export", ".zip");
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {

            for (File file : sourceDir.listFiles()) {
                ZipEntry zipEntry = new ZipEntry(file.getName());
                zos.putNextEntry(zipEntry);

                try (FileInputStream fis = new FileInputStream(file)) {
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) > 0) {
                        zos.write(buffer, 0, length);
                    }
                }
                zos.closeEntry();
            }
        }
        return zipFile;
    }

    private void saveZipFile(File zipFile) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("保存压缩包");
        fileChooser.setInitialFileName("export_data.zip");
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("ZIP Files", "*.zip")
        );

        File saveFile = fileChooser.showSaveDialog(null);
        if (saveFile != null) {
            try (InputStream in = new FileInputStream(zipFile);
                 OutputStream out = new FileOutputStream(saveFile)) {

                byte[] buffer = new byte[1024];
                int length;
                while ((length = in.read(buffer)) > 0) {
                    out.write(buffer, 0, length);
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

    private void deleteTempFiles(File tempDir) {
        for (File file : tempDir.listFiles()) {
            file.delete();
        }
        tempDir.delete();
    }

    @FXML
    private void exportToExcel(ActionEvent event) {
        String exportPath = exportPathField.getText().trim();
        if (exportPath.isEmpty()) {
            showAlert(Alert.AlertType.ERROR, "错误", "请输入导出文件路径");
            return;
        }

        // 确保路径以 .xlsx 结尾
        if (!exportPath.toLowerCase().endsWith(".xlsx")) {
            exportPath += ".xlsx";
        }

        try {
            // 获取当前选中的文件夹
            String selectedFolder = comboBox.getSelectionModel().getSelectedItem();
            if (selectedFolder == null) {
                showAlert(Alert.AlertType.ERROR, "错误", "请先选择一个文件夹");
                return;
            }

            // 获取异常信息数据
            List<ResultInfoDTO> errorData = tableView.getItems();
            if (errorData.isEmpty()) {
                showAlert(Alert.AlertType.WARNING, "警告", "没有异常数据可导出");
                return;
            }

            // 创建工作簿和工作表
            Workbook workbook = new HSSFWorkbook();
            Sheet sheet = workbook.createSheet("异常数据");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("标签");
            headerRow.createCell(1).setCellValue("信息");

            // 填充数据
            for (int i = 0; i < errorData.size(); i++) {
                ResultInfoDTO data = errorData.get(i);
                Row row = sheet.createRow(i + 1);
                row.createCell(0).setCellValue(data.getLabelName());
                row.createCell(1).setCellValue(data.getMessage());
            }

            // 自动调整列宽
            for (int i = 0; i < 2; i++) {
                sheet.autoSizeColumn(i);
            }

            // 保存工作簿到文件
            try (FileOutputStream fileOut = new FileOutputStream(exportPath)) {
                workbook.write(fileOut);
            }

            showAlert(Alert.AlertType.INFORMATION, "成功", "异常数据已成功导出到: " + exportPath);

        } catch (Exception e) {
            e.printStackTrace();
            showAlert(Alert.AlertType.ERROR, "错误", "导出失败: " + e.getMessage());
        }
    }

    private void showAlert(Alert.AlertType alertType, String title, String message) {
        Alert alert = new Alert(alertType);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    /**
     * 调整组件大小以适应窗口大小
     */
    private void adjustComponentSizes() {
        if (rootPane.getScene() == null || rootPane.getScene().getWindow() == null) {
            return;
        }

        // 获取当前窗口大小
        double windowWidth = rootPane.getScene().getWindow().getWidth();
        double windowHeight = rootPane.getScene().getWindow().getHeight();

        // 调整 rootPane 的大小
        rootPane.setPrefWidth(windowWidth);
        rootPane.setPrefHeight(windowHeight);

        // 调整 WebView 的大小
        if (webView != null) {
            webView.setPrefWidth(windowWidth * 0.7); // 使用窗口宽度的70%
            webView.setPrefHeight(windowHeight * 0.8); // 使用窗口高度的80%
        }

        if (featureWebView != null) {
            featureWebView.setPrefWidth(windowWidth * 0.7);
            featureWebView.setPrefHeight(windowHeight * 0.8);
        }

        // 调整表格列宽
        if (labelColumn != null && messageColumn != null) {
            double tableWidth = windowWidth * 0.3 - 20; // 减去一些边距
            labelColumn.setPrefWidth(tableWidth * 0.3); // 30%
            messageColumn.setPrefWidth(tableWidth * 0.7); // 70%
        }

        if (infoColumn != null && infoValueColumn != null) {
            double tableWidth = windowWidth * 0.3 - 20;
            infoColumn.setPrefWidth(tableWidth * 0.3);
            infoValueColumn.setPrefWidth(tableWidth * 0.7);
        }
    }
}
