package org.example.demo;

import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * 时序图数据服务控制器
 * 提供动态数据加载接口，支持缩放时的全量数据获取
 */
@RestController
@RequestMapping("/api/timeseries")
@CrossOrigin(origins = "*")
public class TimeSeriesDataController {
    
    /**
     * 获取指定范围的详细数据
     * 
     * @param dataKey 数据键值
     * @param startIndex 起始索引
     * @param endIndex 结束索引
     * @return 详细数据
     */
    @GetMapping("/detailed-data")
    public Map<String, Object> getDetailedData(
            @RequestParam String dataKey,
            @RequestParam int startIndex,
            @RequestParam int endIndex) {
        
        try {
            // 从缓存的原始数据中获取指定范围的数据
            Map<String, Object> timingMap = ResultController.timingDiagramMap.get(dataKey);
            if (timingMap == null) {
                return createErrorResponse("数据不存在: " + dataKey);
            }
            
            List<String> originalXData = (List<String>) timingMap.get("x");
            List<Double> originalYLoadData = (List<Double>) timingMap.get("yL");
            Object yWeatherObj = timingMap.get("yW");
            
            if (originalXData == null || originalYLoadData == null) {
                return createErrorResponse("数据格式错误");
            }
            
            // 确保索引范围有效
            startIndex = Math.max(0, startIndex);
            endIndex = Math.min(originalXData.size() - 1, endIndex);
            
            if (startIndex > endIndex) {
                return createErrorResponse("索引范围无效");
            }
            
            // 提取指定范围的数据
            List<String> detailedXData = originalXData.subList(startIndex, endIndex + 1);
            List<Double> detailedYLoadData = originalYLoadData.subList(startIndex, 
                Math.min(endIndex + 1, originalYLoadData.size()));
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("xData", detailedXData);
            result.put("yLoadData", detailedYLoadData);
            result.put("startIndex", startIndex);
            result.put("endIndex", endIndex);
            result.put("totalPoints", detailedXData.size());
            
            // 处理气象数据
            if (yWeatherObj instanceof Map) {
                // 多条气象曲线
                Map<String, List<Double>> yWeatherDataMap = (Map<String, List<Double>>) yWeatherObj;
                Map<String, List<Double>> detailedWeatherData = new HashMap<>();
                
                for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                    String curveName = entry.getKey();
                    List<Double> curveData = entry.getValue();
                    List<Double> detailedCurveData = curveData.subList(startIndex, 
                        Math.min(endIndex + 1, curveData.size()));
                    detailedWeatherData.put(curveName, detailedCurveData);
                }
                
                result.put("yWeatherDataMap", detailedWeatherData);
                result.put("isMultiCurve", true);
            } else {
                // 单条气象曲线
                List<Double> yWeatherData = (List<Double>) yWeatherObj;
                List<Double> detailedYWeatherData = yWeatherData.subList(startIndex, 
                    Math.min(endIndex + 1, yWeatherData.size()));
                
                result.put("yWeatherData", detailedYWeatherData);
                result.put("isMultiCurve", false);
            }
            
            System.out.println("返回详细数据: " + dataKey + ", 范围: " + startIndex + "-" + endIndex + 
                             ", 数据点数: " + detailedXData.size());
            
            return result;
            
        } catch (Exception e) {
            e.printStackTrace();
            return createErrorResponse("获取数据时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取数据概览信息
     * 
     * @param dataKey 数据键值
     * @return 数据概览
     */
    @GetMapping("/data-info")
    public Map<String, Object> getDataInfo(@RequestParam String dataKey) {
        try {
            Map<String, Object> timingMap = ResultController.timingDiagramMap.get(dataKey);
            if (timingMap == null) {
                return createErrorResponse("数据不存在: " + dataKey);
            }
            
            List<String> xData = (List<String>) timingMap.get("x");
            List<Double> yLoadData = (List<Double>) timingMap.get("yL");
            Object yWeatherObj = timingMap.get("yW");
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("dataKey", dataKey);
            result.put("totalPoints", xData != null ? xData.size() : 0);
            result.put("startTime", xData != null && !xData.isEmpty() ? xData.get(0) : null);
            result.put("endTime", xData != null && !xData.isEmpty() ? xData.get(xData.size() - 1) : null);
            
            if (yWeatherObj instanceof Map) {
                Map<String, List<Double>> yWeatherDataMap = (Map<String, List<Double>>) yWeatherObj;
                result.put("isMultiCurve", true);
                result.put("curveNames", new ArrayList<>(yWeatherDataMap.keySet()));
                result.put("curveCount", yWeatherDataMap.size());
            } else {
                result.put("isMultiCurve", false);
                result.put("curveCount", 1);
            }
            
            return result;
            
        } catch (Exception e) {
            e.printStackTrace();
            return createErrorResponse("获取数据信息时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 获取可用的数据键列表
     * 
     * @return 数据键列表
     */
    @GetMapping("/data-keys")
    public Map<String, Object> getDataKeys() {
        try {
            Set<String> dataKeys = ResultController.timingDiagramMap.keySet();
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("dataKeys", new ArrayList<>(dataKeys));
            result.put("count", dataKeys.size());
            
            return result;
            
        } catch (Exception e) {
            e.printStackTrace();
            return createErrorResponse("获取数据键列表时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 创建错误响应
     */
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("error", message);
        return result;
    }
}
