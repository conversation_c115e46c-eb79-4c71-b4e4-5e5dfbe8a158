package org.example.demo;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import java.util.*;

/**
 * 时序图显示测试类，验证横坐标显示和鼠标悬停功能
 */
public class TimeSeriesDisplayTest {
    
    private List<String> generateTimeData(int size) {
        List<String> timeData = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            int hour = i / 4; // 每小时4个数据点（15分钟间隔）
            int minute = (i % 4) * 15;
            timeData.add(String.format("2024-01-01 %02d:%02d", hour, minute));
        }
        return timeData;
    }
    
    private List<Double> generateRandomData(int size) {
        List<Double> data = new ArrayList<>();
        Random random = new Random(42); // 固定种子确保可重复性
        for (int i = 0; i < size; i++) {
            data.add(random.nextDouble() * 100 + 50); // 50-150之间的随机数
        }
        return data;
    }
    
    @Test
    public void testDataConsistency() {
        System.out.println("=== 数据一致性测试 ===");
        
        // 测试不同数据量的一致性
        int[] dataSizes = {24, 96, 288, 1440, 8760}; // 1天、4天、12天、60天、365天的小时数据
        
        for (int size : dataSizes) {
            System.out.println("\n测试数据量: " + size + " 个数据点");
            
            List<String> xData = generateTimeData(size);
            List<Double> yData1 = generateRandomData(size);
            List<Double> yData2 = generateRandomData(size);
            
            // 验证数据长度一致性
            System.out.println("X轴数据点数: " + xData.size());
            System.out.println("Y1轴数据点数: " + yData1.size());
            System.out.println("Y2轴数据点数: " + yData2.size());
            
            assert xData.size() == yData1.size() : "X轴和Y1轴数据点数不一致";
            assert xData.size() == yData2.size() : "X轴和Y2轴数据点数不一致";
            
            // 验证时间数据的连续性
            for (int i = 1; i < xData.size(); i++) {
                String prevTime = xData.get(i-1);
                String currTime = xData.get(i);
                System.out.println("时间序列: " + prevTime + " -> " + currTime);
                if (i <= 5) { // 只打印前几个时间点
                    assert currTime.compareTo(prevTime) > 0 : "时间序列不连续";
                }
            }
            
            System.out.println("✓ 数据一致性验证通过");
        }
    }
    
    @Test
    public void testMultiCurveDataConsistency() {
        System.out.println("\n=== 多曲线数据一致性测试 ===");
        
        int dataSize = 1000;
        int curveCount = 3;
        
        List<String> xData = generateTimeData(dataSize);
        List<Double> yLoadData = generateRandomData(dataSize);
        Map<String, List<Double>> yWeatherDataMap = new HashMap<>();
        
        // 生成多条气象曲线
        for (int i = 0; i < curveCount; i++) {
            String curveName = "气象曲线" + (i + 1);
            List<Double> curveData = generateRandomData(dataSize);
            yWeatherDataMap.put(curveName, curveData);
        }
        
        System.out.println("X轴数据点数: " + xData.size());
        System.out.println("负荷数据点数: " + yLoadData.size());
        
        // 验证每条气象曲线的数据一致性
        for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            System.out.println(curveName + " 数据点数: " + curveData.size());
            
            assert xData.size() == curveData.size() : curveName + " 数据点数与X轴不一致";
        }
        
        System.out.println("✓ 多曲线数据一致性验证通过");
    }
    
    @Test
    public void testTimeAxisDisplay() {
        System.out.println("\n=== 时间轴显示测试 ===");
        
        // 测试24小时数据的时间轴显示
        List<String> timeData = new ArrayList<>();
        for (int hour = 0; hour < 24; hour++) {
            timeData.add(String.format("2024-01-01 %02d:00", hour));
        }
        
        System.out.println("生成的时间轴数据:");
        for (int i = 0; i < timeData.size(); i++) {
            System.out.println("  [" + i + "] " + timeData.get(i));
        }
        
        // 验证时间格式
        for (String time : timeData) {
            assert time.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}") : "时间格式不正确: " + time;
        }
        
        System.out.println("✓ 时间轴显示测试通过");
    }
    
    @Test
    public void testHoverDataGeneration() {
        System.out.println("\n=== 鼠标悬停数据生成测试 ===");
        
        int dataSize = 100;
        List<String> xData = generateTimeData(dataSize);
        List<Double> yData1 = generateRandomData(dataSize);
        List<Double> yData2 = generateRandomData(dataSize);
        
        // 模拟生成hover数据
        List<String> hoverData = new ArrayList<>();
        for (int i = 0; i < xData.size(); i++) {
            String hoverText = String.format(
                "时间: %s\\n负荷: %.2f\\n温度: %.2f",
                xData.get(i),
                yData1.get(i),
                yData2.get(i)
            );
            hoverData.add(hoverText);
        }
        
        System.out.println("生成的悬停数据示例:");
        for (int i = 0; i < Math.min(5, hoverData.size()); i++) {
            System.out.println("  [" + i + "] " + hoverData.get(i));
        }
        
        assert hoverData.size() == xData.size() : "悬停数据数量与X轴数据不一致";
        
        System.out.println("✓ 鼠标悬停数据生成测试通过");
    }
    
    @Test
    public void testDataSamplingAccuracy() {
        System.out.println("\n=== 数据采样精度测试 ===");
        
        // 生成大量数据进行采样测试
        int originalSize = 10000;
        int maxPoints = 3000;
        
        List<String> originalXData = generateTimeData(originalSize);
        List<Double> originalYData = generateRandomData(originalSize);
        
        // 模拟采样逻辑
        if (originalSize > maxPoints) {
            int step = Math.max(2, originalSize / maxPoints);
            
            List<String> sampledX = new ArrayList<>();
            List<Double> sampledY = new ArrayList<>();
            
            // 保留第一个数据点
            sampledX.add(originalXData.get(0));
            sampledY.add(originalYData.get(0));
            
            // 采样中间数据点
            for (int i = step; i < originalSize - 1; i += step) {
                sampledX.add(originalXData.get(i));
                sampledY.add(originalYData.get(i));
            }
            
            // 保留最后一个数据点
            if (originalSize > 1) {
                sampledX.add(originalXData.get(originalSize - 1));
                sampledY.add(originalYData.get(originalSize - 1));
            }
            
            System.out.println("原始数据点数: " + originalSize);
            System.out.println("采样后数据点数: " + sampledX.size());
            System.out.println("采样步长: " + step);
            System.out.println("数据压缩比: " + String.format("%.2f%%", (double) sampledX.size() / originalSize * 100));
            
            // 验证采样数据的完整性
            assert sampledX.get(0).equals(originalXData.get(0)) : "第一个数据点未正确保留";
            assert sampledX.get(sampledX.size() - 1).equals(originalXData.get(originalSize - 1)) : "最后一个数据点未正确保留";
            assert sampledX.size() == sampledY.size() : "采样后X轴和Y轴数据点数不一致";
            
            System.out.println("✓ 数据采样精度测试通过");
        }
    }
    
    @Test
    public void testNullDataHandling() {
        System.out.println("\n=== 空值数据处理测试 ===");
        
        // 创建包含null值的测试数据
        List<String> xData = Arrays.asList("2024-01-01 00:00", "2024-01-01 01:00", "2024-01-01 02:00", "2024-01-01 03:00");
        List<Double> yData = Arrays.asList(10.0, null, 20.0, null);
        
        System.out.println("测试数据:");
        for (int i = 0; i < xData.size(); i++) {
            System.out.println("  " + xData.get(i) + " -> " + yData.get(i));
        }
        
        // 验证null值处理
        for (int i = 0; i < yData.size(); i++) {
            Double value = yData.get(i);
            if (value == null) {
                System.out.println("  检测到null值在位置: " + i);
            }
        }
        
        System.out.println("✓ 空值数据处理测试通过");
    }
}
