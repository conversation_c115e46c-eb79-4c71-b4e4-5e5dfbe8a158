package org.example.demo;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import java.util.*;

/**
 * 性能测试类，用于验证时序图优化效果
 */
public class PerformanceTest {
    
    private List<String> generateTimeData(int size) {
        List<String> timeData = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            timeData.add(String.format("2024-01-01 %02d:%02d", i / 60, i % 60));
        }
        return timeData;
    }
    
    private List<Double> generateRandomData(int size) {
        List<Double> data = new ArrayList<>();
        Random random = new Random();
        for (int i = 0; i < size; i++) {
            data.add(random.nextDouble() * 100);
        }
        return data;
    }
    
    @Test
    public void testDataSamplingPerformance() {
        System.out.println("=== 数据采样性能测试 ===");
        
        // 测试不同数据量的性能
        int[] dataSizes = {100, 1000, 5000, 10000, 20000, 50000};
        
        for (int size : dataSizes) {
            System.out.println("\n测试数据量: " + size);
            
            // 生成测试数据
            List<String> xData = generateTimeData(size);
            List<Double> yData1 = generateRandomData(size);
            List<Double> yData2 = generateRandomData(size);
            
            // 测试单曲线性能
            long startTime = System.currentTimeMillis();
            
            // 模拟数据采样逻辑
            final int MAX_POINTS = 2000;
            if (size > MAX_POINTS) {
                int step = size / MAX_POINTS;
                if (step < 2) step = 2;
                
                List<String> sampledX = new ArrayList<>();
                List<Double> sampledY1 = new ArrayList<>();
                List<Double> sampledY2 = new ArrayList<>();
                
                // 保留第一个数据点
                sampledX.add(xData.get(0));
                sampledY1.add(yData1.get(0));
                sampledY2.add(yData2.get(0));
                
                // 采样中间数据点
                for (int i = step; i < size - 1; i += step) {
                    sampledX.add(xData.get(i));
                    sampledY1.add(yData1.get(i));
                    sampledY2.add(yData2.get(i));
                }
                
                // 保留最后一个数据点
                if (size > 1) {
                    sampledX.add(xData.get(size - 1));
                    sampledY1.add(yData1.get(size - 1));
                    sampledY2.add(yData2.get(size - 1));
                }
                
                long endTime = System.currentTimeMillis();
                System.out.println("  采样处理时间: " + (endTime - startTime) + "ms");
                System.out.println("  原始数据点数: " + size + " -> 采样后数据点数: " + sampledX.size());
                System.out.println("  数据压缩比: " + String.format("%.2f%%", (double) sampledX.size() / size * 100));
            } else {
                long endTime = System.currentTimeMillis();
                System.out.println("  无需采样，处理时间: " + (endTime - startTime) + "ms");
            }
        }
    }
    
    @Test
    public void testMultiCurveDataSamplingPerformance() {
        System.out.println("\n=== 多曲线数据采样性能测试 ===");
        
        int dataSize = 10000;
        int curveCount = 5;
        
        System.out.println("数据量: " + dataSize + ", 曲线数量: " + curveCount);
        
        // 生成测试数据
        List<String> xData = generateTimeData(dataSize);
        List<Double> yLoadData = generateRandomData(dataSize);
        Map<String, List<Double>> yWeatherDataMap = new HashMap<>();
        
        for (int i = 0; i < curveCount; i++) {
            yWeatherDataMap.put("曲线" + (i + 1), generateRandomData(dataSize));
        }
        
        long startTime = System.currentTimeMillis();
        
        // 模拟多曲线采样逻辑
        final int MAX_POINTS = 2000;
        if (dataSize > MAX_POINTS) {
            int step = dataSize / MAX_POINTS;
            if (step < 2) step = 2;
            
            List<String> sampledX = new ArrayList<>();
            List<Double> sampledLoad = new ArrayList<>();
            Map<String, List<Double>> sampledWeatherData = new HashMap<>();
            
            // 初始化气象数据集合
            for (String curveName : yWeatherDataMap.keySet()) {
                sampledWeatherData.put(curveName, new ArrayList<>());
            }
            
            // 保留第一个数据点
            sampledX.add(xData.get(0));
            sampledLoad.add(yLoadData.get(0));
            for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                sampledWeatherData.get(entry.getKey()).add(entry.getValue().get(0));
            }
            
            // 采样中间数据点
            for (int i = step; i < dataSize - 1; i += step) {
                sampledX.add(xData.get(i));
                sampledLoad.add(yLoadData.get(i));
                for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                    sampledWeatherData.get(entry.getKey()).add(entry.getValue().get(i));
                }
            }
            
            // 保留最后一个数据点
            if (dataSize > 1) {
                sampledX.add(xData.get(dataSize - 1));
                sampledLoad.add(yLoadData.get(dataSize - 1));
                for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
                    List<Double> curveData = entry.getValue();
                    sampledWeatherData.get(entry.getKey()).add(curveData.get(curveData.size() - 1));
                }
            }
            
            long endTime = System.currentTimeMillis();
            System.out.println("多曲线采样处理时间: " + (endTime - startTime) + "ms");
            System.out.println("原始数据点数: " + dataSize + " -> 采样后数据点数: " + sampledX.size());
            System.out.println("总数据点数（包含所有曲线）: " + (dataSize * (curveCount + 1)) + " -> " + (sampledX.size() * (curveCount + 1)));
        }
    }
    
    @Test
    public void testMemoryUsage() {
        System.out.println("\n=== 内存使用测试 ===");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 测试前的内存使用
        runtime.gc(); // 强制垃圾回收
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("测试前内存使用: " + (memoryBefore / 1024 / 1024) + " MB");
        
        // 创建大量数据
        int dataSize = 50000;
        List<String> xData = generateTimeData(dataSize);
        List<Double> yData1 = generateRandomData(dataSize);
        List<Double> yData2 = generateRandomData(dataSize);
        
        // 测试后的内存使用
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("创建数据后内存使用: " + (memoryAfter / 1024 / 1024) + " MB");
        System.out.println("内存增长: " + ((memoryAfter - memoryBefore) / 1024 / 1024) + " MB");
        
        // 清理数据
        xData = null;
        yData1 = null;
        yData2 = null;
        runtime.gc();
        
        long memoryAfterCleanup = runtime.totalMemory() - runtime.freeMemory();
        System.out.println("清理后内存使用: " + (memoryAfterCleanup / 1024 / 1024) + " MB");
    }
    
    @Test
    public void testCacheEffectiveness() {
        System.out.println("\n=== 缓存效果测试 ===");
        
        // 模拟缓存机制
        Map<String, String> cache = new HashMap<>();
        String testKey = "test_data";
        String testValue = "cached_html_content";
        
        // 第一次访问（无缓存）
        long startTime = System.currentTimeMillis();
        String result1 = cache.get(testKey);
        if (result1 == null) {
            // 模拟生成HTML内容的耗时操作
            try {
                Thread.sleep(100); // 模拟100ms的处理时间
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            cache.put(testKey, testValue);
            result1 = testValue;
        }
        long endTime1 = System.currentTimeMillis();
        System.out.println("第一次访问（生成内容）: " + (endTime1 - startTime) + "ms");
        
        // 第二次访问（使用缓存）
        long startTime2 = System.currentTimeMillis();
        String result2 = cache.get(testKey);
        long endTime2 = System.currentTimeMillis();
        System.out.println("第二次访问（使用缓存）: " + (endTime2 - startTime2) + "ms");
        
        System.out.println("缓存加速比: " + String.format("%.2fx", (double)(endTime1 - startTime) / (endTime2 - startTime2)));
    }
}
