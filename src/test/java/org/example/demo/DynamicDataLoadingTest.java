package org.example.demo;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import java.util.*;

/**
 * 动态数据加载功能测试
 */
public class DynamicDataLoadingTest {
    
    private List<String> generateTimeData(int size) {
        List<String> timeData = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            int hour = i / 4;
            int minute = (i % 4) * 15;
            timeData.add(String.format("2024-01-01 %02d:%02d", hour, minute));
        }
        return timeData;
    }
    
    private List<Double> generateRandomData(int size) {
        List<Double> data = new ArrayList<>();
        Random random = new Random(42);
        for (int i = 0; i < size; i++) {
            data.add(random.nextDouble() * 100 + 50);
        }
        return data;
    }
    
    @Test
    public void testDataSamplingAndFullDataStorage() {
        System.out.println("=== 数据采样和全量数据存储测试 ===");
        
        int originalSize = 10000;
        List<String> xData = generateTimeData(originalSize);
        List<Double> yData1 = generateRandomData(originalSize);
        List<Double> yData2 = generateRandomData(originalSize);
        
        // 模拟数据采样
        final int MAX_POINTS = 3000;
        boolean needsSampling = xData.size() > MAX_POINTS;
        
        System.out.println("原始数据点数: " + originalSize);
        System.out.println("是否需要采样: " + needsSampling);
        
        if (needsSampling) {
            int step = Math.max(2, xData.size() / MAX_POINTS);
            
            List<String> sampledX = new ArrayList<>();
            List<Double> sampledY1 = new ArrayList<>();
            List<Double> sampledY2 = new ArrayList<>();
            
            // 保留第一个数据点
            sampledX.add(xData.get(0));
            sampledY1.add(yData1.get(0));
            sampledY2.add(yData2.get(0));
            
            // 采样中间数据点
            for (int i = step; i < xData.size() - 1; i += step) {
                sampledX.add(xData.get(i));
                sampledY1.add(yData1.get(i));
                sampledY2.add(yData2.get(i));
            }
            
            // 保留最后一个数据点
            if (xData.size() > 1) {
                sampledX.add(xData.get(xData.size() - 1));
                sampledY1.add(yData1.get(yData1.size() - 1));
                sampledY2.add(yData2.get(yData2.size() - 1));
            }
            
            System.out.println("采样后数据点数: " + sampledX.size());
            System.out.println("采样步长: " + step);
            System.out.println("数据压缩比: " + String.format("%.2f%%", (double) sampledX.size() / originalSize * 100));
            
            // 验证采样数据的完整性
            assert sampledX.get(0).equals(xData.get(0)) : "第一个数据点未正确保留";
            assert sampledX.get(sampledX.size() - 1).equals(xData.get(xData.size() - 1)) : "最后一个数据点未正确保留";
            
            System.out.println("✓ 采样数据验证通过");
        }
        
        // 验证全量数据存储
        System.out.println("全量数据存储验证:");
        System.out.println("  X轴数据点数: " + xData.size());
        System.out.println("  Y1轴数据点数: " + yData1.size());
        System.out.println("  Y2轴数据点数: " + yData2.size());
        
        assert xData.size() == yData1.size() : "X轴和Y1轴数据点数不一致";
        assert xData.size() == yData2.size() : "X轴和Y2轴数据点数不一致";
        
        System.out.println("✓ 全量数据存储验证通过");
    }
    
    @Test
    public void testZoomThresholdCalculation() {
        System.out.println("\n=== 缩放阈值计算测试 ===");
        
        int totalDataPoints = 10000;
        double zoomThreshold = 0.1;
        
        // 测试不同的缩放范围
        int[][] zoomRanges = {
            {0, 9999},      // 100% - 整体视图
            {0, 4999},      // 50% - 整体视图
            {0, 1999},      // 20% - 整体视图
            {0, 999},       // 10% - 边界情况
            {0, 499},       // 5% - 细节视图
            {1000, 1099},   // 1% - 细节视图
            {5000, 5049}    // 0.5% - 细节视图
        };
        
        for (int[] range : zoomRanges) {
            int startRange = range[0];
            int endRange = range[1];
            double zoomRatio = (double)(endRange - startRange + 1) / totalDataPoints;
            boolean shouldLoadDetailedData = zoomRatio < zoomThreshold;
            
            System.out.println(String.format("范围: [%d, %d], 比例: %.3f, 加载详细数据: %s", 
                startRange, endRange, zoomRatio, shouldLoadDetailedData ? "是" : "否"));
        }
        
        System.out.println("✓ 缩放阈值计算测试通过");
    }
    
    @Test
    public void testDataSlicing() {
        System.out.println("\n=== 数据切片测试 ===");
        
        List<String> fullXData = generateTimeData(1000);
        List<Double> fullYData = generateRandomData(1000);
        
        // 测试不同的切片范围
        int[][] sliceRanges = {
            {0, 99},        // 前100个数据点
            {450, 549},     // 中间100个数据点
            {900, 999},     // 最后100个数据点
            {100, 199},     // 任意100个数据点
        };
        
        for (int[] range : sliceRanges) {
            int startIndex = range[0];
            int endIndex = range[1];
            
            List<String> slicedX = fullXData.subList(startIndex, endIndex + 1);
            List<Double> slicedY = fullYData.subList(startIndex, endIndex + 1);
            
            System.out.println(String.format("切片范围: [%d, %d], 切片大小: %d", 
                startIndex, endIndex, slicedX.size()));
            System.out.println(String.format("  起始时间: %s, 结束时间: %s", 
                slicedX.get(0), slicedX.get(slicedX.size() - 1)));
            
            // 验证切片数据的一致性
            assert slicedX.size() == slicedY.size() : "切片后X轴和Y轴数据点数不一致";
            assert slicedX.size() == (endIndex - startIndex + 1) : "切片大小不正确";
        }
        
        System.out.println("✓ 数据切片测试通过");
    }
    
    @Test
    public void testMultiCurveDataHandling() {
        System.out.println("\n=== 多曲线数据处理测试 ===");
        
        int dataSize = 5000;
        List<String> xData = generateTimeData(dataSize);
        List<Double> yLoadData = generateRandomData(dataSize);
        
        // 创建多条气象曲线
        Map<String, List<Double>> yWeatherDataMap = new HashMap<>();
        yWeatherDataMap.put("温度", generateRandomData(dataSize));
        yWeatherDataMap.put("湿度", generateRandomData(dataSize));
        yWeatherDataMap.put("风速", generateRandomData(dataSize));
        
        System.out.println("数据规模:");
        System.out.println("  时间轴数据点数: " + xData.size());
        System.out.println("  负荷数据点数: " + yLoadData.size());
        System.out.println("  气象曲线数量: " + yWeatherDataMap.size());
        
        // 验证每条曲线的数据一致性
        for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            
            System.out.println("  " + curveName + " 数据点数: " + curveData.size());
            assert curveData.size() == xData.size() : curveName + " 数据点数与时间轴不一致";
        }
        
        // 测试数据切片
        int startIndex = 1000;
        int endIndex = 1999;
        
        List<String> slicedX = xData.subList(startIndex, endIndex + 1);
        List<Double> slicedLoad = yLoadData.subList(startIndex, endIndex + 1);
        Map<String, List<Double>> slicedWeatherData = new HashMap<>();
        
        for (Map.Entry<String, List<Double>> entry : yWeatherDataMap.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            slicedWeatherData.put(curveName, curveData.subList(startIndex, endIndex + 1));
        }
        
        System.out.println("切片后数据:");
        System.out.println("  切片范围: [" + startIndex + ", " + endIndex + "]");
        System.out.println("  切片大小: " + slicedX.size());
        
        // 验证切片后的数据一致性
        assert slicedX.size() == slicedLoad.size() : "切片后时间轴和负荷数据不一致";
        for (Map.Entry<String, List<Double>> entry : slicedWeatherData.entrySet()) {
            String curveName = entry.getKey();
            List<Double> curveData = entry.getValue();
            assert curveData.size() == slicedX.size() : "切片后" + curveName + "数据与时间轴不一致";
        }
        
        System.out.println("✓ 多曲线数据处理测试通过");
    }
    
    @Test
    public void testPerformanceComparison() {
        System.out.println("\n=== 性能对比测试 ===");
        
        int[] dataSizes = {1000, 5000, 10000, 20000, 50000};
        
        for (int size : dataSizes) {
            System.out.println("\n数据规模: " + size + " 个数据点");
            
            // 生成测试数据
            long startTime = System.currentTimeMillis();
            List<String> xData = generateTimeData(size);
            List<Double> yData1 = generateRandomData(size);
            List<Double> yData2 = generateRandomData(size);
            long dataGenTime = System.currentTimeMillis() - startTime;
            
            // 测试采样性能
            startTime = System.currentTimeMillis();
            final int MAX_POINTS = 3000;
            if (size > MAX_POINTS) {
                int step = Math.max(2, size / MAX_POINTS);
                List<String> sampledX = new ArrayList<>();
                List<Double> sampledY1 = new ArrayList<>();
                
                for (int i = 0; i < size; i += step) {
                    sampledX.add(xData.get(i));
                    sampledY1.add(yData1.get(i));
                }
            }
            long samplingTime = System.currentTimeMillis() - startTime;
            
            // 测试数据切片性能
            startTime = System.currentTimeMillis();
            int sliceStart = size / 4;
            int sliceEnd = size / 2;
            List<String> slicedX = xData.subList(sliceStart, sliceEnd);
            List<Double> slicedY1 = yData1.subList(sliceStart, sliceEnd);
            long slicingTime = System.currentTimeMillis() - startTime;
            
            System.out.println("  数据生成时间: " + dataGenTime + "ms");
            System.out.println("  数据采样时间: " + samplingTime + "ms");
            System.out.println("  数据切片时间: " + slicingTime + "ms");
            System.out.println("  切片数据量: " + slicedX.size() + " 个数据点");
        }
        
        System.out.println("✓ 性能对比测试完成");
    }
}
