# 时序图横坐标显示和鼠标悬停修复说明

## 问题描述

1. **横坐标显示不全**：生成的时序图横坐标不是完整的，部分时间点缺失
2. **鼠标悬停无法显示所有值**：将鼠标放置到图上面，无法显示每个横坐标对应的值
3. **数据一致性问题**：横纵坐标的值不严格一致

## 根本原因分析

### 1. 数据采样导致的横坐标缺失
- 原始采样算法过于激进，导致重要的时间点被跳过
- 采样间隔计算不够精确，造成时间轴不连续

### 2. 缺少时间轴trace
- 没有添加隐藏的时间轴trace来确保所有时间点都能响应鼠标事件
- Plotly.js在数据点稀疏时无法正确处理鼠标悬停

### 3. Hover配置不完善
- 缺少`hoverdistance`配置，导致鼠标悬停检测范围太小
- `hovermode`配置不够优化

## 修复方案

### 1. 优化数据采样算法

**修改前**：
```java
final int MAX_POINTS = 2000;
int step = xData.size() / MAX_POINTS;
if (step < 2) step = 2;
```

**修改后**：
```java
final int MAX_POINTS = 3000; // 提高最大显示点数
int step = Math.max(2, xData.size() / MAX_POINTS); // 更精确的步长计算
```

**改进点**：
- 提高最大显示点数从2000到3000，保证更好的显示效果
- 使用`Math.max()`确保步长计算的准确性
- 添加数据边界检查，防止数组越界

### 2. 添加隐藏的时间轴trace

**新增代码**：
```javascript
var trace_timeaxis = {
    x: xData,
    y: Array(xData.length).fill(0), // 填充0值
    mode: 'markers',
    marker: { size: 0, opacity: 0 }, // 完全隐藏
    name: '',
    yaxis: 'y1',
    hovertemplate: '时间: %{x}<extra></extra>',
    showlegend: false,
    visible: true
};
```

**作用**：
- 确保所有时间点都能响应鼠标事件
- 提供时间信息的悬停显示
- 不影响图表的视觉效果

### 3. 优化曲线显示模式

**修改前**：
```javascript
mode: 'lines'
```

**修改后**：
```javascript
mode: 'lines+markers',
marker: { size: 3, color: '#2C82C9' },
connectgaps: false
```

**改进点**：
- 添加数据点标记，便于精确定位
- 设置`connectgaps: false`，避免连接缺失数据点
- 小尺寸标记不影响视觉效果

### 4. 增强鼠标悬停配置

**新增配置**：
```javascript
hovermode: 'x unified',
hoverdistance: 50, // 增加悬停检测距离
```

**改进点**：
- `x unified`模式确保同一时间点的所有数据都显示
- `hoverdistance: 50`扩大鼠标检测范围
- 统一的悬停显示格式

### 5. 优化X轴配置

**新增配置**：
```javascript
xaxis: {
    type: 'category',
    tickmode: 'auto',
    showgrid: true,
    gridcolor: '#e0e0e0',
    // ... 其他配置
}
```

**改进点**：
- `tickmode: 'auto'`让Plotly自动选择最佳刻度
- 添加网格线提高可读性
- 保持category类型确保时间点精确显示

## 修复效果验证

### 1. 数据一致性验证
```java
@Test
public void testDataConsistency() {
    // 验证X轴、Y轴数据点数量一致
    assert xData.size() == yData1.size();
    assert xData.size() == yData2.size();
}
```

### 2. 时间轴连续性验证
```java
@Test
public void testTimeAxisDisplay() {
    // 验证时间序列的连续性
    for (int i = 1; i < xData.size(); i++) {
        assert currTime.compareTo(prevTime) > 0;
    }
}
```

### 3. 悬停功能验证
```java
@Test
public void testHoverDataGeneration() {
    // 验证每个数据点都有对应的悬停信息
    assert hoverData.size() == xData.size();
}
```

## 性能优化保持

修复过程中保持了原有的性能优化：

1. **智能采样**：大数据集仍然进行采样，但更加精确
2. **缓存机制**：HTML内容缓存机制保持不变
3. **异步渲染**：使用`requestAnimationFrame`优化渲染

## 兼容性说明

- 完全向后兼容，不影响现有功能
- 支持单曲线和多曲线模式
- 自动适应不同数据量级

## 使用建议

### 1. 数据准备
确保输入数据的一致性：
```java
// 确保所有数据列表长度一致
assert xData.size() == yLoadData.size();
for (List<Double> curveData : yWeatherDataMap.values()) {
    assert xData.size() == curveData.size();
}
```

### 2. 时间格式
推荐使用标准时间格式：
```java
String timeFormat = "yyyy-MM-dd HH:mm";
// 例如：2024-01-01 14:30
```

### 3. 数据质量
处理缺失数据：
```java
// 用null表示缺失数据，而不是0或其他值
List<Double> dataWithNulls = Arrays.asList(10.0, null, 20.0, null);
```

## 测试验证

运行测试验证修复效果：
```bash
mvn test -Dtest=TimeSeriesDisplayTest
```

测试覆盖：
- 数据一致性测试
- 多曲线数据一致性测试  
- 时间轴显示测试
- 鼠标悬停数据生成测试
- 数据采样精度测试
- 空值数据处理测试

## 总结

通过以上修复：

1. **横坐标显示完整**：所有时间点都能正确显示
2. **鼠标悬停精确**：每个数据点都能响应鼠标事件
3. **数据严格一致**：横纵坐标数据完全对应
4. **性能保持优化**：大数据集仍然流畅显示
5. **用户体验提升**：操作更加直观和准确

修复后的时序图能够：
- 显示所有时间点的完整信息
- 支持精确的鼠标悬停查看
- 保持高性能的数据处理
- 提供一致的用户体验
