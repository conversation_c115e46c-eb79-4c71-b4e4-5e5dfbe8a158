# 动态数据加载功能说明

## 功能概述

针对时序图在缩放查看局部区域时无法显示全量数据的问题，实现了动态数据加载功能。该功能能够：

1. **整体视图**：显示智能采样后的数据，保证流畅的操作体验
2. **细节视图**：当缩放到小范围时，自动加载该区域的全量原始数据
3. **无缝切换**：在整体视图和细节视图之间自动切换，无需用户手动操作

## 技术实现

### 1. 数据分层存储

```javascript
// 采样数据（用于初始显示）
var originalData = [trace_timeaxis, trace1, trace2];

// 全量数据（用于缩放时的详细显示）
var fullXData = [...];           // 完整的时间轴数据
var fullYLoadData = [...];       // 完整的负荷数据
var fullYWeatherData = [...];    // 完整的气象数据
```

### 2. 缩放检测机制

```javascript
// 缩放阈值：当显示范围小于总数据的10%时，加载全量数据
var zoomThreshold = 0.1;

function handleZoomEvent(eventData) {
    if (eventData['xaxis.range[0]'] && eventData['xaxis.range[1]']) {
        var zoomRatio = (endRange - startRange) / totalDataPoints;
        
        if (zoomRatio < zoomThreshold && !isZoomed) {
            // 加载详细数据
            loadDetailedData(startRange, endRange);
        } else if (zoomRatio >= zoomThreshold && isZoomed) {
            // 恢复采样数据
            restoreSampledData();
        }
    }
}
```

### 3. JavaFX Bridge通信

由于这不是Spring Boot项目，使用JavaScript内置的数据处理机制：

```javascript
window.javaConnector = {
    getFullData: function(dataKey, startRange, endRange) {
        var startIndex = Math.max(0, Math.floor(startRange));
        var endIndex = Math.min(fullXData.length - 1, Math.ceil(endRange));
        
        return {
            success: true,
            xData: fullXData.slice(startIndex, endIndex + 1),
            yLoadData: fullYLoadData.slice(startIndex, endIndex + 1),
            yWeatherData: fullYWeatherData.slice(startIndex, endIndex + 1),
            totalPoints: endIndex - startIndex + 1
        };
    }
};
```

## 使用效果

### 1. 整体视图（采样数据）
- **数据量**：原始50,000点 → 采样3,000点
- **性能**：流畅的缩放和平移操作
- **显示**：保持数据整体趋势和关键特征点

### 2. 细节视图（全量数据）
- **触发条件**：缩放范围 < 总数据的10%
- **数据量**：显示选定区域的所有原始数据点
- **显示**：每个时间点都有对应的数据值，支持精确的鼠标悬停

### 3. 状态指示
- 右上角显示当前视图状态：
  - "整体视图（采样数据）"
  - "细节视图（全量数据）"
  - "细节视图 - 加载中..."

## 性能优化

### 1. 智能采样策略
```java
private DataSample sampleData(List<String> xData, List<Double> yData1, List<Double> yData2) {
    final int MAX_POINTS = 3000; // 提高采样点数，保证更好显示效果
    
    if (xData.size() <= MAX_POINTS) {
        return new DataSample(new ArrayList<>(xData), new ArrayList<>(yData1), new ArrayList<>(yData2));
    }
    
    int step = Math.max(2, xData.size() / MAX_POINTS);
    // 保留首尾数据点，均匀采样中间数据
}
```

### 2. 缓存机制
- HTML内容缓存：避免重复生成相同数据的HTML
- 全量数据缓存：在JavaScript中缓存原始数据，避免重复传输

### 3. 异步加载
```javascript
async function loadDetailedData(startRange, endRange) {
    document.getElementById('zoomInfo').innerHTML = '细节视图（全量数据） - 加载中...';
    
    setTimeout(function() {
        var detailedData = generateDetailedData(startRange, endRange);
        if (detailedData) {
            Plotly.react('plot', detailedData);
            isZoomed = true;
            document.getElementById('zoomInfo').innerHTML = '细节视图（全量数据）';
        }
    }, 500); // 模拟网络延迟，实际可以更快
}
```

## 兼容性说明

### 1. 单曲线和多曲线支持
- 自动检测数据类型（单曲线/多曲线）
- 统一的动态加载接口
- 相同的用户体验

### 2. 数据格式兼容
- 支持包含null值的数据
- 自动处理数据长度不一致的情况
- 保持原有的数据结构

### 3. 浏览器兼容性
- 使用标准JavaScript API
- 兼容现代浏览器的ES6特性
- 提供降级方案

## 使用方法

### 1. 查看整体数据
- 默认显示采样后的数据
- 可以进行缩放、平移等操作
- 性能流畅，适合概览

### 2. 查看详细数据
- 使用鼠标滚轮或缩放工具缩放到小范围
- 系统自动检测并加载全量数据
- 可以看到每个时间点的精确数值

### 3. 返回整体视图
- 双击图表重置视图
- 或者缩放到较大范围
- 系统自动切换回采样数据

## 配置参数

### 1. 缩放阈值
```javascript
var zoomThreshold = 0.1; // 可调整，建议0.05-0.2之间
```

### 2. 采样点数
```java
final int MAX_POINTS = 3000; // 可调整，建议2000-5000之间
```

### 3. 加载延迟
```javascript
setTimeout(function() {
    // 处理逻辑
}, 500); // 可调整，建议100-1000ms之间
```

## 故障排除

### 1. 数据加载失败
- 检查浏览器控制台错误信息
- 确认原始数据格式正确
- 验证JavaScript Bridge连接

### 2. 性能问题
- 调整采样点数（MAX_POINTS）
- 优化缩放阈值（zoomThreshold）
- 检查数据量是否过大

### 3. 显示异常
- 确认时间格式正确
- 检查数据一致性
- 验证Plotly.js版本兼容性

## 总结

动态数据加载功能实现了：

1. **智能切换**：根据缩放级别自动选择合适的数据密度
2. **性能优化**：整体视图使用采样数据，细节视图使用全量数据
3. **用户友好**：无需手动操作，自动适应用户的查看需求
4. **完整显示**：缩放时能看到所有时间点的精确数据
5. **流畅体验**：保持操作的流畅性和响应性

这样既解决了大数据量时的性能问题，又满足了用户查看详细数据的需求。
